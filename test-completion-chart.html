<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>完成率图表测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
</head>
<body class="bg-gray-50 p-8">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-3xl font-bold text-center mb-8">完成率图表实时更新测试</h1>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- 测试说明 -->
            <div class="bg-white rounded-lg p-6 shadow-md">
                <h2 class="text-xl font-bold mb-4 text-blue-600">
                    <i class="fa fa-info-circle mr-2"></i>测试说明
                </h2>
                <div class="space-y-3 text-sm">
                    <div class="bg-yellow-50 border-l-4 border-yellow-400 p-3">
                        <p class="font-medium text-yellow-800">问题描述：</p>
                        <p class="text-yellow-700">之前的完成率图表只显示历史数据，当天的数据需要切换到其他日期后才会更新。</p>
                    </div>
                    <div class="bg-green-50 border-l-4 border-green-400 p-3">
                        <p class="font-medium text-green-800">修复内容：</p>
                        <ul class="text-green-700 list-disc list-inside space-y-1">
                            <li>图表现在包含当天的实时数据</li>
                            <li>任务状态变化时立即更新图表</li>
                            <li>自动存档过期数据</li>
                            <li>今天的数据标记为"今天"</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 测试步骤 -->
            <div class="bg-white rounded-lg p-6 shadow-md">
                <h2 class="text-xl font-bold mb-4 text-purple-600">
                    <i class="fa fa-list-ol mr-2"></i>测试步骤
                </h2>
                <ol class="space-y-3 text-sm">
                    <li class="flex items-start">
                        <span class="bg-purple-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs mr-3 mt-0.5 flex-shrink-0">1</span>
                        <span>打开主计划页面，查看底部的"任务完成率趋势"图表</span>
                    </li>
                    <li class="flex items-start">
                        <span class="bg-purple-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs mr-3 mt-0.5 flex-shrink-0">2</span>
                        <span>添加几个今天的任务，观察图表是否显示"今天"的数据点</span>
                    </li>
                    <li class="flex items-start">
                        <span class="bg-purple-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs mr-3 mt-0.5 flex-shrink-0">3</span>
                        <span>勾选/取消勾选任务，观察图表中今天的完成率是否实时变化</span>
                    </li>
                    <li class="flex items-start">
                        <span class="bg-purple-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs mr-3 mt-0.5 flex-shrink-0">4</span>
                        <span>切换到其他日期再切换回今天，确认数据正常显示</span>
                    </li>
                </ol>
            </div>
        </div>

        <!-- 数据管理工具 -->
        <div class="mt-8 bg-white rounded-lg p-6 shadow-md">
            <h2 class="text-xl font-bold mb-4 text-orange-600">
                <i class="fa fa-database mr-2"></i>数据管理工具
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <button onclick="checkData()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                    <i class="fa fa-search mr-2"></i>检查数据状态
                </button>
                <button onclick="createTestData()" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded">
                    <i class="fa fa-plus mr-2"></i>创建测试数据
                </button>
                <button onclick="clearTestData()" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded">
                    <i class="fa fa-trash mr-2"></i>清除测试数据
                </button>
            </div>
            <div id="data-results" class="bg-gray-100 p-4 rounded text-sm"></div>
        </div>

        <!-- 快速跳转 -->
        <div class="mt-8 text-center">
            <a href="main-plan.html" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-medium transition-colors mr-4">
                <i class="fa fa-external-link mr-2"></i>打开主计划页面
            </a>
            <a href="final-test.html" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                <i class="fa fa-home mr-2"></i>返回测试首页
            </a>
        </div>
    </div>

    <script>
        function getDateKey(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        }

        function checkData() {
            const data = JSON.parse(localStorage.getItem('dailyPlanner')) || {};
            const results = document.getElementById('data-results');
            
            let output = '<strong>数据状态检查：</strong><br><br>';
            
            // 检查任务数据
            const tasks = data.tasks || {};
            const taskDates = Object.keys(tasks);
            output += `任务数据：${taskDates.length} 个日期<br>`;
            
            // 检查历史数据
            const history = data.history || {};
            const historyDates = Object.keys(history);
            output += `历史数据：${historyDates.length} 个日期<br><br>`;
            
            // 检查今天的数据
            const today = new Date();
            const todayKey = getDateKey(today);
            const todayTasks = tasks[todayKey] || [];
            const todayHistory = history[todayKey];
            
            output += `<strong>今天 (${todayKey}) 的数据：</strong><br>`;
            output += `• 当前任务：${todayTasks.length} 个<br>`;
            output += `• 历史记录：${todayHistory ? '存在' : '不存在'}<br>`;
            
            if (todayTasks.length > 0) {
                const completed = todayTasks.filter(t => t.completed).length;
                const rate = Math.round((completed / todayTasks.length) * 100);
                output += `• 完成率：${completed}/${todayTasks.length} (${rate}%)<br>`;
            }
            
            // 显示所有有数据的日期
            const allDates = [...new Set([...taskDates, ...historyDates])].sort();
            if (allDates.length > 0) {
                output += '<br><strong>所有数据日期：</strong><br>';
                allDates.forEach(date => {
                    const hasTask = tasks[date] && tasks[date].length > 0;
                    const hasHistory = history[date];
                    const status = hasHistory ? '已存档' : (hasTask ? '当前数据' : '无数据');
                    output += `• ${date}: ${status}<br>`;
                });
            }
            
            results.innerHTML = output;
        }

        function createTestData() {
            const data = JSON.parse(localStorage.getItem('dailyPlanner')) || {
                tasks: {},
                improvements: {},
                reflections: {},
                ratings: {},
                history: {}
            };
            
            // 创建过去几天的测试数据
            for (let i = 3; i >= 1; i--) {
                const date = new Date();
                date.setDate(date.getDate() - i);
                const dateKey = getDateKey(date);
                
                // 创建任务
                const tasks = [
                    { id: `test_${dateKey}_1`, text: `测试任务1 - ${dateKey}`, completed: true, category: 'life' },
                    { id: `test_${dateKey}_2`, text: `测试任务2 - ${dateKey}`, completed: true, category: 'work' },
                    { id: `test_${dateKey}_3`, text: `测试任务3 - ${dateKey}`, completed: false, category: 'study' }
                ];
                
                data.tasks[dateKey] = tasks;
                
                // 直接存档到历史
                data.history[dateKey] = {
                    tasks: [...tasks],
                    improvements: [],
                    reflections: [],
                    rating: 4
                };
            }
            
            // 创建今天的测试数据（不存档）
            const today = new Date();
            const todayKey = getDateKey(today);
            data.tasks[todayKey] = [
                { id: `test_${todayKey}_1`, text: `今天任务1`, completed: true, category: 'life' },
                { id: `test_${todayKey}_2`, text: `今天任务2`, completed: false, category: 'work' }
            ];
            
            localStorage.setItem('dailyPlanner', JSON.stringify(data));
            
            document.getElementById('data-results').innerHTML = 
                '<span style="color: green;">✅ 测试数据已创建，包含过去3天的历史数据和今天的当前数据</span>';
        }

        function clearTestData() {
            const data = JSON.parse(localStorage.getItem('dailyPlanner')) || {};
            
            // 清除测试任务
            if (data.tasks) {
                Object.keys(data.tasks).forEach(dateKey => {
                    data.tasks[dateKey] = data.tasks[dateKey].filter(task => !task.id.startsWith('test_'));
                    if (data.tasks[dateKey].length === 0) {
                        delete data.tasks[dateKey];
                    }
                });
            }
            
            // 清除测试历史
            if (data.history) {
                Object.keys(data.history).forEach(dateKey => {
                    if (data.history[dateKey].tasks.some(task => task.id.startsWith('test_'))) {
                        delete data.history[dateKey];
                    }
                });
            }
            
            localStorage.setItem('dailyPlanner', JSON.stringify(data));
            
            document.getElementById('data-results').innerHTML = 
                '<span style="color: red;">🗑️ 测试数据已清除</span>';
        }

        // 页面加载时自动检查数据
        window.addEventListener('load', checkData);
    </script>
</body>
</html>
