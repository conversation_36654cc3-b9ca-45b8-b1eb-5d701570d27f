<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生活NOTE - 最终测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#4F46E5',
                        secondary: '#10B981',
                        accent: '#F59E0B',
                        light: '#F5F7FF',
                        dark: '#1E293B'
                    }
                }
            }
        }
    </script>
    <style>
        .nav-link {
            color: #6B7280;
            font-weight: 500;
            transition: all 0.2s ease;
            padding: 0.5rem 0;
            position: relative;
            display: flex;
            align-items: center;
            text-decoration: none;
        }
        .nav-link:hover { color: #F59E0B; }
        .nav-link.active { color: #F59E0B; font-weight: 600; }
        .nav-link::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: #F59E0B;
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }
        .nav-link:hover::after,
        .nav-link.active::after { transform: scaleX(1); }
        .mobile-nav-link {
            color: #6B7280;
            border-radius: 0.375rem;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            text-decoration: none;
        }
        .mobile-nav-link:hover { color: #3B82F6; background-color: #F3F4F6; }
        .mobile-nav-link.active { color: #3B82F6; background-color: rgba(59, 130, 246, 0.1); font-weight: 600; }
        
        .test-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        .test-card:hover {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }
        .status-good { color: #10B981; }
        .status-warning { color: #F59E0B; }
        .status-error { color: #EF4444; }
    </style>
</head>
<body class="bg-gray-50 text-gray-800">
    <!-- 统一导航栏 -->
    <nav class="bg-white shadow-md sticky top-0 left-0 right-0 z-50 transition-all duration-300">
        <div class="container mx-auto px-4">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="main-plan.html" class="flex items-center">
                        <i class="fa fa-apple text-green-500 text-2xl mr-2"></i>
                        <span class="font-bold text-xl text-gray-800">生活NOTE</span>
                    </a>
                </div>
                <div class="hidden md:flex items-center space-x-8">
                    <a href="main-plan.html" class="nav-link">
                        <i class="fa fa-home mr-2"></i>主计划
                    </a>
                    <a href="expense-tracker.html" class="nav-link">
                        <i class="fa fa-money mr-2"></i>支出记录
                    </a>
                    <a href="wishlist.html" class="nav-link">
                        <i class="fa fa-star mr-2"></i>愿望清单
                    </a>
                    <a href="settings.html" class="nav-link">
                        <i class="fa fa-cog mr-2"></i>设置
                    </a>
                </div>
                <div class="md:hidden flex items-center">
                    <button id="mobile-menu-button" class="text-gray-600 hover:text-blue-500 focus:outline-none">
                        <i class="fa fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>
        <div id="mobile-menu" class="hidden md:hidden bg-white shadow-lg w-full">
            <div class="px-2 pt-2 pb-3 space-y-1">
                <a href="main-plan.html" class="mobile-nav-link block px-3 py-2 rounded-md">
                    <i class="fa fa-home mr-2"></i>主计划
                </a>
                <a href="expense-tracker.html" class="mobile-nav-link block px-3 py-2 rounded-md">
                    <i class="fa fa-money mr-2"></i>支出记录
                </a>
                <a href="wishlist.html" class="mobile-nav-link block px-3 py-2 rounded-md">
                    <i class="fa fa-star mr-2"></i>愿望清单
                </a>
                <a href="settings.html" class="mobile-nav-link block px-3 py-2 rounded-md">
                    <i class="fa fa-cog mr-2"></i>设置
                </a>
            </div>
        </div>
    </nav>

    <main class="container mx-auto px-4 py-8" style="padding-top: 2rem;">
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-blue-600 mb-4">🎉 修复完成测试</h1>
            <p class="text-gray-600 text-lg">所有问题已修复，请测试各个页面功能</p>
        </div>

        <!-- 修复状态总结 -->
        <div class="test-card mb-8">
            <h2 class="text-2xl font-bold text-green-600 mb-4">
                <i class="fa fa-check-circle mr-2"></i>修复状态总结
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="space-y-2">
                    <p class="status-good"><i class="fa fa-check mr-2"></i>导航栏显示问题 - 已修复</p>
                    <p class="status-good"><i class="fa fa-check mr-2"></i>支出记录添加功能 - 已修复</p>
                    <p class="status-good"><i class="fa fa-check mr-2"></i>数据完整性 - 已保护</p>
                    <p class="status-good"><i class="fa fa-check mr-2"></i>移动端菜单 - 已修复</p>
                </div>
                <div class="space-y-2">
                    <p class="status-good"><i class="fa fa-check mr-2"></i>统一样式系统 - 已实现</p>
                    <p class="status-good"><i class="fa fa-check mr-2"></i>代码重复问题 - 已解决</p>
                    <p class="status-good"><i class="fa fa-check mr-2"></i>调试日志 - 已添加</p>
                    <p class="status-good"><i class="fa fa-check mr-2"></i>错误处理 - 已改进</p>
                </div>
            </div>
        </div>

        <!-- 页面测试 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="test-card">
                <h3 class="text-lg font-bold text-blue-600 mb-4">
                    <i class="fa fa-home mr-2"></i>主计划页面
                </h3>
                <p class="text-gray-600 mb-4">测试任务管理、感想记录等功能</p>
                <a href="main-plan.html" class="block bg-blue-500 text-white px-4 py-2 rounded text-center hover:bg-blue-600 transition">
                    测试页面
                </a>
            </div>

            <div class="test-card">
                <h3 class="text-lg font-bold text-purple-600 mb-4">
                    <i class="fa fa-star mr-2"></i>愿望清单页面
                </h3>
                <p class="text-gray-600 mb-4">测试长期目标和愿望管理功能</p>
                <a href="wishlist.html" class="block bg-purple-500 text-white px-4 py-2 rounded text-center hover:bg-purple-600 transition">
                    测试页面
                </a>
            </div>

            <div class="test-card">
                <h3 class="text-lg font-bold text-yellow-600 mb-4">
                    <i class="fa fa-money mr-2"></i>支出记录页面
                </h3>
                <p class="text-gray-600 mb-4">测试支出记录添加功能</p>
                <a href="expense-tracker.html" class="block bg-yellow-500 text-white px-4 py-2 rounded text-center hover:bg-yellow-600 transition">
                    测试页面
                </a>
            </div>
        </div>

        <!-- 数据检查工具 -->
        <div class="test-card">
            <h2 class="text-2xl font-bold text-purple-600 mb-4">
                <i class="fa fa-database mr-2"></i>数据完整性验证
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <button onclick="checkAllData()" class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600 transition">
                    <i class="fa fa-search mr-2"></i>检查所有数据
                </button>
                <button onclick="exportBackup()" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition">
                    <i class="fa fa-download mr-2"></i>导出备份
                </button>
                <a href="data-recovery.html" class="bg-orange-500 text-white px-4 py-2 rounded hover:bg-orange-600 transition text-center">
                    <i class="fa fa-wrench mr-2"></i>数据恢复工具
                </a>
            </div>
            <div id="data-check-results" class="bg-gray-100 p-4 rounded text-sm"></div>
        </div>
    </main>

    <script>
        // 移动端菜单切换
        document.getElementById('mobile-menu-button').addEventListener('click', function() {
            document.getElementById('mobile-menu').classList.toggle('hidden');
        });

        // 检查所有数据
        function checkAllData() {
            const results = document.getElementById('data-check-results');
            let output = '<strong>数据完整性检查结果：</strong><br><br>';
            
            // 检查支出记录数据
            const expenses = localStorage.getItem('expenses');
            if (expenses) {
                try {
                    const data = JSON.parse(expenses);
                    output += `<span class="status-good">✅ 支出记录数据：存在，共 ${data.length} 条记录</span><br>`;
                } catch (e) {
                    output += `<span class="status-error">❌ 支出记录数据：格式错误</span><br>`;
                }
            } else {
                output += `<span class="status-warning">⚠️ 支出记录数据：不存在</span><br>`;
            }
            
            // 检查主计划数据
            const dailyPlanner = localStorage.getItem('dailyPlanner');
            if (dailyPlanner) {
                try {
                    const data = JSON.parse(dailyPlanner);
                    const taskDays = Object.keys(data.tasks || {}).length;
                    const reflectionDays = Object.keys(data.reflections || {}).length;
                    output += `<span class="status-good">✅ 主计划数据：存在，${taskDays} 天任务，${reflectionDays} 天感想</span><br>`;
                } catch (e) {
                    output += `<span class="status-error">❌ 主计划数据：格式错误</span><br>`;
                }
            } else {
                output += `<span class="status-warning">⚠️ 主计划数据：不存在</span><br>`;
            }
            

            
            // 检查周信息
            const weekInfo = localStorage.getItem('currentWeekInfo');
            if (weekInfo) {
                output += `<span class="status-good">✅ 当前周信息：存在</span><br>`;
            } else {
                output += `<span class="status-warning">⚠️ 当前周信息：不存在</span><br>`;
            }
            
            output += '<br><strong>总结：</strong>数据检查完成，请根据结果进行相应处理。';
            results.innerHTML = output;
        }

        // 导出备份
        function exportBackup() {
            const allData = {};
            for (let key in localStorage) {
                if (localStorage.hasOwnProperty(key)) {
                    try {
                        allData[key] = JSON.parse(localStorage.getItem(key));
                    } catch (e) {
                        allData[key] = localStorage.getItem(key);
                    }
                }
            }
            
            const dataStr = JSON.stringify(allData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `生活NOTE_完整备份_${new Date().toISOString().split('T')[0]}.json`;
            link.click();
            
            document.getElementById('data-check-results').innerHTML = '<strong>✅ 数据备份已导出到下载文件夹</strong>';
        }

        // 页面加载时自动检查数据
        window.addEventListener('load', function() {
            checkAllData();
        });
    </script>
</body>
</html>
