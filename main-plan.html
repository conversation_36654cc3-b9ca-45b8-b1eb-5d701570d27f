<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>每日计划 - 生活NOTE</title>
    <!-- 引入统一样式 -->
    <link rel="stylesheet" href="common-styles.css">
    <!-- 引入Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- 引入Font Awesome -->
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <!-- 引入Chart.js用于绘制图表 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.8/dist/chart.umd.min.js"></script>
    <!-- 引入通用工具 -->
    <script src="common-utils.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#4F46E5',
                        secondary: '#818CF8',
                        success: '#10B981',
                        study: '#3B82F6',    // 学习类别颜色
                        work: '#10B981',     // 工作类别颜色
                        life: '#F59E0B',     // 生活类别颜色
                        improvement: '#A78BFA', // 下次改进颜色
                        light: '#F5F7FF',
                        dark: '#1E293B'
                    },
                    fontFamily: {
                        sans: ['Inter', 'system-ui', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            .task-complete {
                @apply line-through text-gray-400;
            }
            .card-shadow {
                @apply shadow-md hover:shadow-lg transition-shadow duration-300;
            }
            .btn-effect {
                @apply transform hover:-translate-y-0.5 transition-transform duration-200;
            }
            .reflection-card {
                @apply bg-white border border-gray-100 rounded-lg p-4 mb-3 hover:shadow-md transition-shadow;
            }
            .calendar-day {
                @apply w-8 h-8 flex items-center justify-center rounded-full cursor-pointer hover:bg-light;
            }
            .calendar-day-selected {
                @apply bg-primary text-white;
            }
            .calendar-day-today {
                @apply ring-2 ring-primary ring-offset-2;
            }
            .notification {
                @apply fixed bottom-4 right-4 px-4 py-3 rounded-lg shadow-lg z-50 transform transition-all duration-300 translate-y-10 opacity-0;
            }
            .notification.show {
                @apply translate-y-0 opacity-100;
            }
            .notification-success {
                @apply bg-success text-white;
            }
            .notification-error {
                @apply bg-red-500 text-white;
            }

            /* 导航栏样式 */
            .nav-link {
                color: #6B7280;
                font-weight: 500;
                transition: all 0.2s ease;
                padding: 0.5rem 0;
                position: relative;
                display: flex;
                align-items: center;
                text-decoration: none;
            }

            .nav-link:hover {
                color: #F59E0B;
            }

            .nav-link.active {
                color: #F59E0B;
                font-weight: 600;
            }

            .nav-link::after {
                content: '';
                position: absolute;
                bottom: 0;
                left: 0;
                width: 100%;
                height: 2px;
                background-color: #F59E0B;
                transform: scaleX(0);
                transition: transform 0.3s ease;
            }

            .nav-link:hover::after,
            .nav-link.active::after {
                transform: scaleX(1);
            }

            .mobile-nav-link {
                color: #6B7280;
                border-radius: 0.375rem;
                transition: all 0.2s ease;
                display: flex;
                align-items: center;
                text-decoration: none;
            }

            .mobile-nav-link:hover {
                color: #3B82F6;
                background-color: #F3F4F6;
            }

            .mobile-nav-link.active {
                color: #3B82F6;
                background-color: rgba(59, 130, 246, 0.1);
                font-weight: 600;
            }

            /* 假期按钮样式 */
            #toggleHoliday {
                background: linear-gradient(135deg, #f59e0b 0%, #f97316 100%);
                color: white;
                border: none;
                box-shadow: 0 2px 4px rgba(245, 158, 11, 0.3);
            }

            #toggleHoliday:hover {
                background: linear-gradient(135deg, #d97706 0%, #ea580c 100%);
                box-shadow: 0 4px 8px rgba(245, 158, 11, 0.4);
                transform: translateY(-1px);
            }

            #toggleHoliday.holiday-active {
                background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
            }

            #toggleHoliday.holiday-active:hover {
                background: linear-gradient(135deg, #059669 0%, #047857 100%);
                box-shadow: 0 4px 8px rgba(16, 185, 129, 0.4);
            }

            /* 假期模式下的页面样式 */
            .holiday-mode {
                background: linear-gradient(135deg, #fef3c7 0%, #fed7aa 100%);
            }

            .holiday-mode .card-shadow {
                background: rgba(255, 255, 255, 0.8);
                backdrop-filter: blur(10px);
            }
        }
    </style>
</head>
<body class="bg-gray-50 font-sans text-dark">
    <!-- 统一导航栏 -->
    <nav class="bg-white shadow-md sticky top-0 left-0 right-0 z-50 transition-all duration-300">
        <div class="container mx-auto px-4">
            <div class="flex justify-between h-16">
                <!-- 左侧Logo/标题 -->
                <div class="flex items-center">
                    <a href="main-plan.html" class="flex items-center">
                        <i class="fa fa-apple text-secondary text-2xl mr-2"></i>
                        <span class="font-bold text-xl text-dark">生活NOTE</span>
                    </a>
                </div>

                <!-- 右侧导航链接 - 桌面端 -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="main-plan.html" class="nav-link active" data-page="main-plan">
                        <i class="fa fa-home mr-2"></i>主计划
                    </a>
                    <a href="expense-tracker.html" class="nav-link" data-page="expense-tracker">
                        <i class="fa fa-money mr-2"></i>支出记录
                    </a>
                    <a href="wishlist.html" class="nav-link" data-page="wishlist">
                        <i class="fa fa-star mr-2"></i>愿望清单
                    </a>
                    <a href="settings.html" class="nav-link" data-page="settings">
                        <i class="fa fa-cog mr-2"></i>设置
                    </a>
                </div>

                <!-- 移动端菜单按钮 -->
                <div class="md:hidden flex items-center">
                    <button id="mobile-menu-button" class="text-gray-600 hover:text-primary focus:outline-none">
                        <i class="fa fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- 移动端导航菜单 -->
        <div id="mobile-menu" class="hidden md:hidden bg-white shadow-lg w-full">
            <div class="px-2 pt-2 pb-3 space-y-1">
                <a href="main-plan.html" class="mobile-nav-link active block px-3 py-2 rounded-md" data-page="main-plan">
                    <i class="fa fa-home mr-2"></i>主计划
                </a>
                <a href="expense-tracker.html" class="mobile-nav-link block px-3 py-2 rounded-md" data-page="expense-tracker">
                    <i class="fa fa-money mr-2"></i>支出记录
                </a>
                <a href="wishlist.html" class="mobile-nav-link block px-3 py-2 rounded-md" data-page="wishlist">
                    <i class="fa fa-star mr-2"></i>愿望清单
                </a>
                <a href="settings.html" class="mobile-nav-link block px-3 py-2 rounded-md" data-page="settings">
                    <i class="fa fa-cog mr-2"></i>设置
                </a>
            </div>
        </div>
    </nav>
    <div class="container mx-auto px-4 py-6 max-w-6xl" style="padding-top: 5rem;">
        <!-- 头部标题和日期 -->
        <header class="mb-6 text-center">
            <h1 class="text-[clamp(1.5rem,4vw,2.2rem)] font-bold text-primary mb-2">每日计划</h1>
            <div class="flex items-center justify-center space-x-3 mb-3">
                <button id="prevDay" class="p-2 rounded-full hover:bg-light text-primary">
                    <i class="fa fa-chevron-left"></i>
                </button>
                <h2 id="currentDate" class="text-xl font-medium"></h2>
                <button id="openCalendar" class="p-2 rounded-full hover:bg-light text-primary">
                    <i class="fa fa-calendar"></i>
                </button>
                <button id="nextDay" class="p-2 rounded-full hover:bg-light text-primary">
                    <i class="fa fa-chevron-right"></i>
                </button>
            </div>
            <!-- 假期控制按钮 -->
            <div class="flex items-center justify-center space-x-3">
                <button id="toggleHoliday" class="px-4 py-2 rounded-lg font-medium transition-all duration-200 flex items-center">
                    <i class="fa fa-umbrella-beach mr-2"></i>
                    <span id="holidayButtonText">设为假期</span>
                </button>
                <div id="holidayIndicator" class="hidden px-3 py-1 bg-orange-100 text-orange-800 rounded-full text-sm font-medium">
                    <i class="fa fa-sun mr-1"></i>假期模式
                </div>
            </div>
        </header>

        <!-- 日历模态框 -->
        <div id="calendarModal" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50 hidden">
            <div class="bg-white rounded-xl w-full max-w-md p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-bold text-primary">选择日期</h3>
                    <button id="closeCalendar" class="text-gray-500 hover:text-gray-700">
                        <i class="fa fa-times"></i>
                    </button>
                </div>
                <div class="flex justify-between items-center mb-4">
                    <button id="prevMonth" class="p-1 hover:bg-light rounded">
                        <i class="fa fa-chevron-left"></i>
                    </button>
                    <h4 id="currentMonth" class="font-medium"></h4>
                    <button id="nextMonth" class="p-1 hover:bg-light rounded">
                        <i class="fa fa-chevron-right"></i>
                    </button>
                </div>
                <div class="grid grid-cols-7 gap-1 text-center mb-2">
                    <div class="text-sm text-gray-500">日</div>
                    <div class="text-sm text-gray-500">一</div>
                    <div class="text-sm text-gray-500">二</div>
                    <div class="text-sm text-gray-500">三</div>
                    <div class="text-sm text-gray-500">四</div>
                    <div class="text-sm text-gray-500">五</div>
                    <div class="text-sm text-gray-500">六</div>
                </div>
                <div id="calendarDays" class="grid grid-cols-7 gap-1 text-center">
                    <!-- 日历天数将动态生成 -->
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- 左侧：计划列表 -->
            <div class="lg:col-span-2">
                <!-- 核心计划 -->
                <div class="bg-white rounded-xl p-5 card-shadow mb-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-bold flex items-center text-red-600">
                            <i class="fa fa-star mr-2"></i> 核心计划
                        </h3>
                        <span id="coreTaskStats" class="text-sm text-gray-500">
                            已完成 <span id="coreCompletedCount">0</span>/<span id="coreTotalCount">0</span>
                        </span>
                    </div>

                    <!-- 添加新核心计划 -->
                    <div class="mb-5">
                        <div class="flex">
                            <input
                                type="text"
                                id="newCoreTaskInput"
                                placeholder="添加核心计划（重要且紧急的任务）..."
                                class="flex-1 px-4 py-2 border border-gray-300 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-red-500/50 focus:border-red-500"
                            >
                            <button id="addCoreTaskBtn" class="bg-red-600 text-white px-4 py-2 rounded-r-lg font-medium btn-effect">
                                <i class="fa fa-plus"></i>
                            </button>
                        </div>
                        <div class="flex items-center mt-2 text-xs text-gray-500">
                            <div class="flex items-center">
                                <span class="w-3 h-3 rounded-full bg-red-600 mr-1"></span>
                                <span>核心计划会计入每日完成率统计</span>
                            </div>
                        </div>
                    </div>

                    <!-- 核心计划列表 -->
                    <div id="coreTaskList" class="max-h-[200px] overflow-y-auto space-y-2">
                        <!-- 核心计划项将通过JavaScript动态生成 -->
                        <div id="emptyCoreState" class="text-center py-8 text-gray-400">
                            <i class="fa fa-star text-3xl mb-2 opacity-30 text-red-600/50"></i>
                            <p>还没有核心计划，添加最重要的任务吧！</p>
                        </div>
                    </div>
                </div>

                <!-- 今日计划 -->
                <div class="bg-white rounded-xl p-5 card-shadow">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-bold flex items-center text-primary">
                            <i class="fa fa-list-alt mr-2"></i> 今日计划
                        </h3>
                        <span id="taskStats" class="text-sm text-gray-500">
                            已完成 <span id="completedCount">0</span>/<span id="totalCount">0</span>
                        </span>
                    </div>

                    <!-- 添加新计划 -->
                    <div class="mb-5">
                        <div class="flex">
                            <input 
                                type="text" 
                                id="newTaskInput" 
                                placeholder="添加新的计划..." 
                                class="flex-1 px-4 py-2 border border-gray-300 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
                            >
                            <select id="taskCategory" class="border-y border-gray-300 px-3 bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary">
                                <option value="study">学习</option>
                                <option value="work">工作</option>
                                <option value="life">生活</option>
                            </select>
                            <button id="addTaskBtn" class="bg-primary text-white px-4 py-2 rounded-r-lg font-medium btn-effect">
                                <i class="fa fa-plus"></i>
                            </button>
                        </div>
                        <div class="flex items-center mt-2 text-xs text-gray-500 space-x-4">
                            <div class="flex items-center">
                                <span class="w-3 h-3 rounded-full bg-study mr-1"></span>
                                <span>学习</span>
                            </div>
                            <div class="flex items-center">
                                <span class="w-3 h-3 rounded-full bg-work mr-1"></span>
                                <span>工作</span>
                            </div>
                            <div class="flex items-center">
                                <span class="w-3 h-3 rounded-full bg-life mr-1"></span>
                                <span>生活</span>
                            </div>
                        </div>
                    </div>

                    <!-- 计划列表 -->
                    <div id="taskList" class="max-h-[300px] overflow-y-auto space-y-2">
                        <!-- 计划项将通过JavaScript动态生成 -->
                        <div id="emptyState" class="text-center py-10 text-gray-400">
                            <i class="fa fa-check-square-o text-4xl mb-3 opacity-30"></i>
                            <p>今天还没有计划，添加一个吧！</p>
                        </div>
                    </div>
                </div>

                <!-- 持续改进和待办事项 -->
                <div class="bg-white rounded-xl p-5 card-shadow mt-6">
                    <!-- 标签切换 -->
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex bg-gray-100 rounded-lg p-1">
                            <button id="improvementTab" class="px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 bg-improvement text-white">
                                <i class="fa fa-lightbulb-o mr-2"></i>持续改进
                            </button>
                            <button id="todoTab" class="px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 text-gray-600 hover:text-gray-900">
                                <i class="fa fa-check-square-o mr-2"></i>待办事项
                            </button>
                        </div>
                        <span id="currentTabStats" class="text-sm text-gray-500">
                            共 <span id="currentTabCount">0</span> 项
                        </span>
                    </div>

                    <!-- 持续改进部分 -->
                    <div id="improvementSection">
                        <!-- 添加新改进项 -->
                        <div class="mb-5">
                            <div class="flex">
                                <input
                                    type="text"
                                    id="newImprovementInput"
                                    placeholder="添加需要改进的地方..."
                                    class="flex-1 px-4 py-2 border border-gray-300 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-improvement/50 focus:border-improvement"
                                >
                                <button id="addImprovementBtn" class="bg-improvement text-white px-4 py-2 rounded-r-lg font-medium btn-effect">
                                    <i class="fa fa-plus"></i>
                                </button>
                            </div>
                            <div class="flex items-center mt-2 text-xs text-gray-500">
                                <div class="flex items-center">
                                    <span class="w-3 h-3 rounded-full bg-improvement mr-1"></span>
                                    <span>每天自动重置为未完成状态，培养持续改进习惯</span>
                                </div>
                            </div>
                        </div>

                        <!-- 改进项列表 -->
                        <div id="improvementList" class="max-h-[250px] overflow-y-auto space-y-2">
                            <!-- 改进项将通过JavaScript动态生成 -->
                            <div id="emptyImprovementState" class="text-center py-10 text-gray-400">
                                <i class="fa fa-lightbulb-o text-4xl mb-3 opacity-30 text-improvement/50"></i>
                                <p>还没有添加改进项，记录一下需要改进的地方吧！</p>
                            </div>
                        </div>
                    </div>

                    <!-- 待办事项部分 -->
                    <div id="todoSection" class="hidden">
                        <!-- 添加新待办事项 -->
                        <div class="mb-5">
                            <div class="flex">
                                <input
                                    type="text"
                                    id="newTodoInput"
                                    placeholder="添加待办事项..."
                                    class="flex-1 px-4 py-2 border border-gray-300 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500"
                                >
                                <button id="addTodoBtn" class="bg-blue-600 text-white px-4 py-2 rounded-r-lg font-medium btn-effect">
                                    <i class="fa fa-plus"></i>
                                </button>
                            </div>
                            <div class="flex items-center mt-2 text-xs text-gray-500">
                                <div class="flex items-center">
                                    <span class="w-3 h-3 rounded-full bg-blue-600 mr-1"></span>
                                    <span>可完成的待办事项，完成后不会自动重置</span>
                                </div>
                            </div>
                        </div>

                        <!-- 待办事项列表 -->
                        <div id="todoList" class="max-h-[250px] overflow-y-auto space-y-2">
                            <!-- 待办事项将通过JavaScript动态生成 -->
                            <div id="emptyTodoState" class="text-center py-10 text-gray-400">
                                <i class="fa fa-check-square-o text-4xl mb-3 opacity-30 text-blue-600/50"></i>
                                <p>还没有添加待办事项，记录一下要做的事情吧！</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 完成进度和评分 -->
                <div class="bg-white rounded-xl p-5 card-shadow mt-6">
                    <h3 class="text-lg font-bold flex items-center text-primary mb-4">
                        <i class="fa fa-pie-chart mr-2"></i> 完成进度
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="flex justify-center">
                            <div class="relative w-32 h-32">
                                <svg class="w-full h-full" viewBox="0 0 36 36">
                                    <circle cx="18" cy="18" r="16" fill="none" stroke="#E5E7EB" stroke-width="3" />
                                    <circle 
                                        id="progressRing" 
                                        cx="18" cy="18" r="16" 
                                        fill="none" stroke="#10B981" 
                                        stroke-width="3" 
                                        stroke-dasharray="100" 
                                        stroke-dashoffset="100" 
                                        transform="rotate(-90 18 18)"
                                        class="transition-all duration-1000 ease-out"
                                    />
                                </svg>
                                <div class="absolute inset-0 flex items-center justify-center">
                                    <span id="progressPercent" class="text-2xl font-bold text-success">0%</span>
                                </div>
                            </div>
                        </div>

                        <!-- 评分部分 -->
                        <div>
                            <h4 class="text-base font-medium mb-3">今日完成度评分</h4>
                            <div class="flex items-center space-x-2">
                                <div id="ratingStars" class="flex text-gray-300">
                                    <i class="fa fa-star text-xl cursor-pointer hover:text-yellow-400" data-rating="1"></i>
                                    <i class="fa fa-star text-xl cursor-pointer hover:text-yellow-400" data-rating="2"></i>
                                    <i class="fa fa-star text-xl cursor-pointer hover:text-yellow-400" data-rating="3"></i>
                                    <i class="fa fa-star text-xl cursor-pointer hover:text-yellow-400" data-rating="4"></i>
                                    <i class="fa fa-star text-xl cursor-pointer hover:text-yellow-400" data-rating="5"></i>
                                </div>
                                <span id="ratingText" class="text-gray-500 ml-2">未评分</span>
                            </div>
                            <div class="mt-3 space-y-2 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-gray-500">核心计划</span>
                                    <span id="coreTasksDisplay" class="text-red-600">0/0</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-500">今日计划</span>
                                    <span id="dailyTasksDisplay" class="text-blue-600">0/0</span>
                                </div>
                                <div class="flex justify-between border-t pt-2">
                                    <span class="text-gray-700 font-medium">总计划</span>
                                    <span id="totalTasks" class="font-medium">0</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-500">已完成</span>
                                    <span id="finishedTasks" class="text-success">0</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-500">未完成</span>
                                    <span id="pendingTasks">0</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧：今日感想和数据导出 -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-xl p-5 card-shadow h-full flex flex-col">
                    <h3 class="text-lg font-bold flex items-center text-primary mb-4">
                        <i class="fa fa-pencil mr-2"></i> 今日感想
                    </h3>
                    
                    <!-- 添加新感想 -->
                    <div class="mb-6">
                        <textarea 
                            id="newReflectionInput" 
                            placeholder="添加新的感想..." 
                            class="w-full h-24 p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary resize-none"
                        ></textarea>
                        <button id="addReflectionBtn" class="mt-3 bg-secondary text-white px-4 py-2 rounded-lg text-sm font-medium btn-effect">
                            <i class="fa fa-plus mr-1"></i> 添加
                        </button>
                    </div>
                    
                    <!-- 感想列表 -->
                    <div id="reflectionsContainer" class="flex-1 overflow-y-auto space-y-3 mb-6">
                        <!-- 感想卡片将通过JavaScript动态生成 -->
                        <div id="noReflections" class="text-center py-8 text-gray-400">
                            <i class="fa fa-comment-o text-3xl mb-2 opacity-30"></i>
                            <p>还没有添加感想</p>
                        </div>
                    </div>

                    <!-- 数据导出和导入 -->
                    <div class="border-t border-gray-100 pt-4">
                        <div class="flex flex-col space-y-2">
                            <button id="exportDataBtn" class="w-full bg-light text-primary px-4 py-2 rounded-lg text-sm font-medium btn-effect flex items-center justify-center">
                                <i class="fa fa-download mr-1"></i> 导出数据
                            </button>
                            <div class="relative">
                                <label for="importDataBtn" class="w-full bg-light text-primary px-4 py-2 rounded-lg text-sm font-medium btn-effect flex items-center justify-center cursor-pointer">
                                    <i class="fa fa-upload mr-1"></i> 导入数据
                                </label>
                                <input type="file" id="importDataBtn" accept=".json" class="absolute inset-0 opacity-0 cursor-pointer">
                            </div>
                        </div>
                        <p id="exportStatus" class="mt-2 text-sm text-center hidden"></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 历史记录 -->
        <div class="mt-6 text-center">
            <button id="showHistoryBtn" class="bg-light text-primary px-4 py-2 rounded-lg font-medium btn-effect">
                <i class="fa fa-history mr-1"></i> 查看历史记录
            </button>
        </div>

        <!-- 历史记录模态框 -->
        <div id="historyModal" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50 hidden">
            <div class="bg-white rounded-xl max-w-2xl w-full max-h-[80vh] overflow-hidden flex flex-col">
                <div class="bg-primary text-white p-4 flex justify-between items-center">
                    <h3 class="text-lg font-bold">历史记录</h3>
                    <button id="closeHistoryBtn" class="text-white hover:text-gray-200">
                        <i class="fa fa-times"></i>
                    </button>
                </div>
                <div class="p-4 overflow-y-auto flex-1" id="historyContent">
                    <!-- 历史记录内容将通过JavaScript动态生成 -->
                </div>
                <div class="p-4 border-t border-gray-100">
                    <button id="clearHistoryBtn" class="text-red-500 hover:text-red-600 hover:bg-red-50 px-4 py-2 rounded-lg text-sm font-medium">
                        <i class="fa fa-trash mr-1"></i> 清除所有历史
                    </button>
                </div>
            </div>
        </div>

        <!-- 任务完成率曲线 -->
        <div class="mt-8 bg-white rounded-xl p-5 card-shadow">
            <h3 class="text-lg font-bold flex items-center text-primary mb-4">
                <i class="fa fa-line-chart mr-2"></i> 任务完成率趋势
            </h3>
            <div class="h-64">
                <canvas id="completionRateChart"></canvas>
            </div>
            <p id="noHistoryData" class="text-center py-10 text-gray-400 hidden">
                <i class="fa fa-bar-chart text-3xl mb-2 opacity-30"></i>
                <p>暂无历史数据可显示</p>
            </p>
        </div>
    </div>

    <!-- 通知组件 -->
    <div id="notification" class="notification"></div>

    <script>
        // 确保DOM加载完成后再执行
        document.addEventListener('DOMContentLoaded', function() {
            try {
                // 日期处理工具
                const formatDate = (date) => {
                    const options = { year: 'numeric', month: 'long', day: 'numeric', weekday: 'long' };
                    return date.toLocaleDateString('zh-CN', options);
                };

                const formatMonth = (date) => {
                    const options = { year: 'numeric', month: 'long' };
                    return date.toLocaleDateString('zh-CN', options);
                };

                const getDateKey = (date) => {
                    const year = date.getFullYear();
                    const month = String(date.getMonth() + 1).padStart(2, '0');
                    const day = String(date.getDate()).padStart(2, '0');
                    return `${year}-${month}-${day}`;
                };

                // 全局状态
                let currentDate = new Date();
                let calendarDate = new Date(); // 用于日历显示的日期
                let appData = {};
                let completionChart = null; // 图表实例
                
                // 安全初始化本地存储数据
                try {
                    const storedData = localStorage.getItem('dailyPlanner');
                    if (storedData) {
                        appData = JSON.parse(storedData);
                    } else {
                        // 初始化新数据结构
                        appData = {
                            tasks: {},
                            coreTasks: {}, // 新增：核心计划
                            improvements: [], // 修改：全局改进项（每天重置）
                            todos: [], // 新增：待办事项（可完成）
                            reflections: {},
                            ratings: {},
                            history: {},
                            holidays: {} // 新增：假期记录
                        };
                    }
                } catch (e) {
                    console.error('本地存储数据解析错误，重新初始化:', e);
                    appData = {
                        tasks: {},
                        coreTasks: {}, // 新增：核心计划
                        improvements: [], // 修改：全局改进项（每天重置）
                        todos: [], // 新增：待办事项（可完成）
                        reflections: {},
                        ratings: {},
                        history: {},
                        holidays: {} // 新增：假期记录
                    };
                    localStorage.setItem('dailyPlanner', JSON.stringify(appData));
                }

                // 确保数据结构完整
                if (!appData.tasks) appData.tasks = {};
                if (!appData.coreTasks) appData.coreTasks = {}; // 新增：核心计划
                if (!appData.improvements) appData.improvements = []; // 修改：全局改进项
                if (!appData.todos) appData.todos = []; // 新增：待办事项
                if (!appData.reflections) appData.reflections = {};
                if (!appData.ratings) appData.ratings = {};
                if (!appData.history) appData.history = {};
                if (!appData.holidays) appData.holidays = {}; // 新增：假期记录

                // 数据迁移：如果improvements是对象格式，转换为数组格式
                if (typeof appData.improvements === 'object' && !Array.isArray(appData.improvements)) {
                    const oldImprovements = appData.improvements;
                    appData.improvements = [];
                    // 将所有日期的改进项合并到全局数组中
                    Object.keys(oldImprovements).forEach(dateKey => {
                        if (Array.isArray(oldImprovements[dateKey])) {
                            oldImprovements[dateKey].forEach(item => {
                                appData.improvements.push({
                                    ...item,
                                    addedDate: dateKey // 保留原始添加日期
                                });
                            });
                        }
                    });
                    console.log('已迁移改进项数据到全局格式');
                }

                // DOM元素 - 增加存在性检查
                const elements = {};
                const elementIds = [
                    'currentDate', 'newTaskInput', 'addTaskBtn', 'taskList', 'emptyState',
                    'completedCount', 'totalCount', 'progressRing', 'progressPercent',
                    'totalTasks', 'finishedTasks', 'pendingTasks', 'ratingStars', 'ratingText',
                    'newReflectionInput', 'addReflectionBtn', 'reflectionsContainer', 'noReflections',
                    'prevDay', 'nextDay', 'showHistoryBtn', 'historyModal', 'closeHistoryBtn',
                    'historyContent', 'clearHistoryBtn', 'taskCategory', 'openCalendar',
                    'calendarModal', 'closeCalendar', 'currentMonth', 'prevMonth', 'nextMonth',
                    'calendarDays', 'exportDataBtn', 'exportStatus', 'importDataBtn', 'notification',
                    'completionRateChart', 'noHistoryData',
                    // 核心计划相关元素
                    'newCoreTaskInput', 'addCoreTaskBtn', 'coreTaskList', 'emptyCoreState',
                    'coreCompletedCount', 'coreTotalCount', 'coreTasksDisplay',
                    // 今日计划显示
                    'dailyTasksDisplay',
                    // 改进项相关元素
                    'newImprovementInput', 'addImprovementBtn', 'improvementList',
                    'emptyImprovementState', 'improvementCount',
                    // 待办事项相关元素
                    'newTodoInput', 'addTodoBtn', 'todoList', 'emptyTodoState'
                ];
                
                elementIds.forEach(id => {
                    elements[id] = document.getElementById(id);
                    if (!elements[id]) {
                        console.warn(`元素 ${id} 未找到`);
                    }
                });

                // 显示通知
                const showNotification = (message, type = 'info') => {
                    if (!elements.notification) return;
                    
                    // 设置通知内容和样式
                    elements.notification.textContent = message;
                    elements.notification.className = `notification notification-${type} show`;
                    
                    // 3秒后隐藏
                    setTimeout(() => {
                        elements.notification.classList.remove('show');
                    }, 3000);
                };

                // 更新显示的日期
                const updateDisplayDate = () => {
                    if (elements.currentDate) {
                        elements.currentDate.textContent = formatDate(currentDate);
                    }
                };

                // 创建核心任务元素
                const createCoreTaskElement = (task, index, totalTasks) => {
                    const taskEl = document.createElement('div');
                    taskEl.className = `flex items-center p-3 border border-gray-100 rounded-lg hover:bg-gray-50 ${task.completed ? 'bg-red-50' : ''}`;
                    taskEl.dataset.taskId = task.id;

                    taskEl.innerHTML = `
                        <input type="checkbox" class="task-checkbox mr-3 w-4 h-4 text-red-600 border-gray-300 rounded focus:ring-red-500" ${task.completed ? 'checked' : ''}>
                        <span class="flex-1 ${task.completed ? 'line-through text-gray-400' : ''}">
                            <span class="inline-block w-2 h-2 rounded-full bg-red-600 mr-2"></span>
                            <span class="task-text cursor-pointer hover:bg-gray-100 px-1 rounded" contenteditable="false">${task.text}</span>
                        </span>
                        <div class="flex items-center space-x-1 ml-auto">
                            <button class="edit-core-task text-blue-400 hover:text-blue-600 p-1" title="编辑核心任务">
                                <i class="fa fa-edit"></i>
                            </button>
                            <button class="move-core-task-up text-gray-400 hover:text-red-600 p-1 ${index === 0 ? 'opacity-30 cursor-not-allowed' : ''}" title="上移">
                                <i class="fa fa-chevron-up"></i>
                            </button>
                            <button class="move-core-task-down text-gray-400 hover:text-red-600 p-1 ${index === totalTasks - 1 ? 'opacity-30 cursor-not-allowed' : ''}" title="下移">
                                <i class="fa fa-chevron-down"></i>
                            </button>
                            <button class="delete-core-task text-gray-400 hover:text-red-500 p-1" title="删除核心任务">
                                <i class="fa fa-trash-o"></i>
                            </button>
                        </div>
                    `;

                    // 添加事件监听
                    const checkbox = taskEl.querySelector('.task-checkbox');
                    if (checkbox) {
                        checkbox.addEventListener('change', () => {
                            toggleCoreTaskCompletion(task.id);
                        });
                    }

                    const editBtn = taskEl.querySelector('.edit-core-task');
                    const textSpan = taskEl.querySelector('.task-text');
                    if (editBtn && textSpan) {
                        editBtn.addEventListener('click', () => {
                            if (textSpan.contentEditable === 'false') {
                                textSpan.contentEditable = 'true';
                                textSpan.focus();
                                textSpan.style.backgroundColor = '#f3f4f6';
                                editBtn.innerHTML = '<i class="fa fa-check"></i>';
                                editBtn.title = '保存';
                            } else {
                                const newText = textSpan.textContent.trim();
                                updateCoreTaskText(task.id, newText);
                                textSpan.contentEditable = 'false';
                                textSpan.style.backgroundColor = '';
                                editBtn.innerHTML = '<i class="fa fa-edit"></i>';
                                editBtn.title = '编辑核心任务';
                            }
                        });

                        textSpan.addEventListener('keypress', (e) => {
                            if (e.key === 'Enter') {
                                e.preventDefault();
                                editBtn.click();
                            }
                        });
                    }

                    const deleteBtn = taskEl.querySelector('.delete-core-task');
                    if (deleteBtn) {
                        deleteBtn.addEventListener('click', () => {
                            deleteCoreTask(task.id);
                        });
                    }

                    const moveUpBtn = taskEl.querySelector('.move-core-task-up');
                    if (moveUpBtn && index > 0) {
                        moveUpBtn.addEventListener('click', () => {
                            moveCoreTask(task.id, -1);
                        });
                    }

                    const moveDownBtn = taskEl.querySelector('.move-core-task-down');
                    if (moveDownBtn && index < totalTasks - 1) {
                        moveDownBtn.addEventListener('click', () => {
                            moveCoreTask(task.id, 1);
                        });
                    }

                    return taskEl;
                };

                // 创建任务元素
                const createTaskElement = (task, index, totalTasks) => {
                    // 根据类别获取样式和图标
                    const categoryStyles = {
                        study: {
                            bgClass: 'bg-study',
                            icon: 'fa-book'
                        },
                        work: {
                            bgClass: 'bg-work',
                            icon: 'fa-briefcase'
                        },
                        life: {
                            bgClass: 'bg-life',
                            icon: 'fa-home'
                        }
                    };
                    
                    const style = categoryStyles[task.category] || categoryStyles.study;
                    
                    const taskEl = document.createElement('div');
                    taskEl.className = 'flex items-center p-3 border border-gray-100 rounded-lg hover:bg-gray-50';
                    taskEl.dataset.taskId = task.id;

                    taskEl.innerHTML = `
                        <input type="checkbox" class="task-checkbox w-5 h-5 rounded text-success focus:ring-success" ${task.completed ? 'checked' : ''}>
                        <div class="ml-2 flex-1 ${task.completed ? 'task-complete' : ''}">
                            <span class="inline-block w-2 h-2 rounded-full ${style.bgClass} mr-2"></span>
                            <span class="task-text cursor-pointer hover:bg-gray-100 px-1 rounded" contenteditable="false">${task.text}</span>
                        </div>
                        <div class="flex items-center space-x-1 ml-auto">
                            <select class="task-category-select text-xs border border-gray-300 rounded px-2 py-1" style="background-color: ${style.bgColor};">
                                <option value="study" ${task.category === 'study' ? 'selected' : ''}>学习</option>
                                <option value="work" ${task.category === 'work' ? 'selected' : ''}>工作</option>
                                <option value="life" ${task.category === 'life' ? 'selected' : ''}>生活</option>
                                <option value="improvement" ${task.category === 'improvement' ? 'selected' : ''}>改进</option>
                            </select>
                            <button class="edit-task text-blue-400 hover:text-blue-600 p-1" title="编辑任务">
                                <i class="fa fa-edit"></i>
                            </button>
                            <button class="move-task-up text-gray-400 hover:text-primary p-1 ${index === 0 ? 'opacity-30 cursor-not-allowed' : ''}" title="上移">
                                <i class="fa fa-chevron-up"></i>
                            </button>
                            <button class="move-task-down text-gray-400 hover:text-primary p-1 ${index === totalTasks - 1 ? 'opacity-30 cursor-not-allowed' : ''}" title="下移">
                                <i class="fa fa-chevron-down"></i>
                            </button>
                            <button class="delete-task text-gray-400 hover:text-red-500 p-1" title="删除任务">
                                <i class="fa fa-trash-o"></i>
                            </button>
                        </div>
                    `;

                    // 添加事件监听
                    const checkbox = taskEl.querySelector('.task-checkbox');
                    const taskText = taskEl.querySelector('.task-text');
                    const categorySelect = taskEl.querySelector('.task-category-select');
                    const editBtn = taskEl.querySelector('.edit-task');
                    const deleteBtn = taskEl.querySelector('.delete-task');
                    const moveUpBtn = taskEl.querySelector('.move-task-up');
                    const moveDownBtn = taskEl.querySelector('.move-task-down');

                    if (checkbox) {
                        checkbox.addEventListener('change', () => {
                            toggleTaskCompletion(task.id);
                        });
                    }

                    // 编辑任务文本
                    if (editBtn && taskText) {
                        editBtn.addEventListener('click', () => {
                            if (taskText.contentEditable === 'false') {
                                taskText.contentEditable = 'true';
                                taskText.focus();
                                taskText.style.backgroundColor = '#FEF3C7';
                                editBtn.innerHTML = '<i class="fa fa-check"></i>';
                                editBtn.title = '保存';
                            } else {
                                taskText.contentEditable = 'false';
                                taskText.style.backgroundColor = '';
                                editBtn.innerHTML = '<i class="fa fa-edit"></i>';
                                editBtn.title = '编辑任务';
                                updateTaskText(task.id, taskText.textContent.trim());
                            }
                        });

                        // 按Enter键保存编辑
                        taskText.addEventListener('keydown', (e) => {
                            if (e.key === 'Enter') {
                                e.preventDefault();
                                taskText.contentEditable = 'false';
                                taskText.style.backgroundColor = '';
                                editBtn.innerHTML = '<i class="fa fa-edit"></i>';
                                editBtn.title = '编辑任务';
                                updateTaskText(task.id, taskText.textContent.trim());
                            }
                        });
                    }

                    // 类别选择变化
                    if (categorySelect) {
                        categorySelect.addEventListener('change', () => {
                            updateTaskCategory(task.id, categorySelect.value);
                        });
                    }

                    if (deleteBtn) {
                        deleteBtn.addEventListener('click', () => {
                            if (confirm('确定要删除这个任务吗？')) {
                                deleteTask(task.id);
                            }
                        });
                    }

                    if (moveUpBtn && index > 0) {
                        moveUpBtn.addEventListener('click', () => {
                            moveTask(task.id, -1); // 上移
                        });
                    }

                    if (moveDownBtn && index < totalTasks - 1) {
                        moveDownBtn.addEventListener('click', () => {
                            moveTask(task.id, 1); // 下移
                        });
                    }

                    return taskEl;
                };

                // 加载核心计划列表
                const loadCoreTasks = () => {
                    if (!elements.coreTaskList || !elements.emptyCoreState) return;

                    const dateKey = getDateKey(currentDate);
                    const coreTasks = appData.coreTasks[dateKey] || [];

                    // 清空列表
                    elements.coreTaskList.innerHTML = '';

                    if (coreTasks.length === 0) {
                        // 显示空状态
                        elements.coreTaskList.appendChild(elements.emptyCoreState);
                    } else {
                        // 添加核心任务
                        coreTasks.forEach((task, index) => {
                            const taskEl = createCoreTaskElement(task, index, coreTasks.length);
                            elements.coreTaskList.appendChild(taskEl);
                        });
                    }

                    // 更新统计
                    updateTaskStats();
                };

                // 添加新核心任务
                const addCoreTask = () => {
                    if (!elements.newCoreTaskInput) return;

                    const taskText = elements.newCoreTaskInput.value.trim();
                    if (!taskText) {
                        showNotification('请输入核心任务内容', 'error');
                        return;
                    }

                    const dateKey = getDateKey(currentDate);
                    if (!appData.coreTasks[dateKey]) {
                        appData.coreTasks[dateKey] = [];
                    }

                    const newTask = {
                        id: Date.now().toString(),
                        text: taskText,
                        completed: false
                    };

                    appData.coreTasks[dateKey].push(newTask);
                    saveData();
                    loadCoreTasks();
                    updateCompletionRateChart();

                    // 清空输入框
                    elements.newCoreTaskInput.value = '';
                    elements.newCoreTaskInput.focus();
                    showNotification('核心任务添加成功', 'success');
                };

                // 切换核心任务完成状态
                const toggleCoreTaskCompletion = (taskId) => {
                    const dateKey = getDateKey(currentDate);
                    const coreTasks = appData.coreTasks[dateKey] || [];

                    const task = coreTasks.find(t => t.id === taskId);
                    if (task) {
                        task.completed = !task.completed;
                        saveData();
                        loadCoreTasks();
                        updateCompletionRateChart();
                    }
                };

                // 更新核心任务文本
                const updateCoreTaskText = (taskId, newText) => {
                    if (!newText.trim()) {
                        showNotification('核心任务内容不能为空', 'error');
                        loadCoreTasks();
                        return;
                    }

                    const dateKey = getDateKey(currentDate);
                    const coreTasks = appData.coreTasks[dateKey] || [];

                    const task = coreTasks.find(t => t.id === taskId);
                    if (task) {
                        task.text = newText;
                        saveData();
                        showNotification('核心任务已更新', 'success');
                    }
                };

                // 删除核心任务
                const deleteCoreTask = (taskId) => {
                    const dateKey = getDateKey(currentDate);
                    if (appData.coreTasks[dateKey]) {
                        appData.coreTasks[dateKey] = appData.coreTasks[dateKey].filter(t => t.id !== taskId);
                        saveData();
                        loadCoreTasks();
                        updateCompletionRateChart();
                        showNotification('核心任务已删除', 'success');
                    }
                };

                // 移动核心任务
                const moveCoreTask = (taskId, direction) => {
                    const dateKey = getDateKey(currentDate);
                    const coreTasks = appData.coreTasks[dateKey] || [];
                    const taskIndex = coreTasks.findIndex(t => t.id === taskId);

                    if (taskIndex === -1) return;

                    const newIndex = taskIndex + direction;
                    if (newIndex < 0 || newIndex >= coreTasks.length) return;

                    [coreTasks[taskIndex], coreTasks[newIndex]] = [coreTasks[newIndex], coreTasks[taskIndex]];

                    saveData();
                    loadCoreTasks();
                    updateCompletionRateChart();
                };

                // 移动任务
                const moveTask = (taskId, direction) => {
                    const dateKey = getDateKey(currentDate);
                    const tasks = appData.tasks[dateKey] || [];
                    const taskIndex = tasks.findIndex(t => t.id === taskId);
                    
                    if (taskIndex === -1) return;
                    
                    // 计算新位置
                    const newIndex = taskIndex + direction;
                    
                    // 检查边界
                    if (newIndex < 0 || newIndex >= tasks.length) return;
                    
                    // 交换位置
                    [tasks[taskIndex], tasks[newIndex]] = [tasks[newIndex], tasks[taskIndex]];
                    
                    saveData();
                    loadTasks();
                    updateCompletionRateChart(); // 更新图表
                };

                // 加载任务列表
                const loadTasks = () => {
                    if (!elements.taskList || !elements.emptyState) return;

                    const dateKey = getDateKey(currentDate);
                    const tasks = appData.tasks[dateKey] || [];

                    // 清空列表
                    elements.taskList.innerHTML = '';

                    if (tasks.length === 0) {
                        // 显示空状态
                        elements.taskList.appendChild(elements.emptyState);
                    } else {
                        // 按类别排序：生活类排在前面，然后是工作、学习、改进
                        const categoryOrder = { 'life': 1, 'work': 2, 'study': 3, 'improvement': 4 };
                        const sortedTasks = [...tasks].sort((a, b) => {
                            // 确保任务有category属性，兼容旧数据
                            if (!a.category) a.category = 'study';
                            if (!b.category) b.category = 'study';

                            const orderA = categoryOrder[a.category] || 5;
                            const orderB = categoryOrder[b.category] || 5;

                            // 如果类别相同，保持原有顺序（按添加时间）
                            if (orderA === orderB) {
                                return tasks.indexOf(a) - tasks.indexOf(b);
                            }

                            return orderA - orderB;
                        });

                        // 添加任务
                        sortedTasks.forEach((task, index) => {
                            const taskEl = createTaskElement(task, index, sortedTasks.length);
                            elements.taskList.appendChild(taskEl);
                        });
                    }

                    // 更新统计
                    updateTaskStats();
                };

                // 添加新任务
                const addTask = () => {
                    if (!elements.newTaskInput || !elements.taskCategory) return;
                    
                    const taskText = elements.newTaskInput.value.trim();
                    if (!taskText) {
                        showNotification('请输入任务内容', 'error');
                        return;
                    }

                    const category = elements.taskCategory.value;
                    const dateKey = getDateKey(currentDate);
                    if (!appData.tasks[dateKey]) {
                        appData.tasks[dateKey] = [];
                    }

                    const newTask = {
                        id: Date.now().toString(),
                        text: taskText,
                        completed: false,
                        category: category
                    };

                    appData.tasks[dateKey].push(newTask);
                    saveData();
                    loadTasks();
                    updateCompletionRateChart(); // 更新图表

                    // 清空输入框
                    elements.newTaskInput.value = '';
                    elements.newTaskInput.focus();
                    showNotification('任务添加成功', 'success');
                };

                // 切换任务完成状态
                const toggleTaskCompletion = (taskId) => {
                    const dateKey = getDateKey(currentDate);
                    const tasks = appData.tasks[dateKey] || [];

                    const task = tasks.find(t => t.id === taskId);
                    if (task) {
                        task.completed = !task.completed;
                        saveData();
                        loadTasks();
                        updateCompletionRateChart(); // 更新图表
                    }
                };

                // 更新任务文本
                const updateTaskText = (taskId, newText) => {
                    if (!newText.trim()) {
                        showNotification('任务内容不能为空', 'error');
                        loadTasks(); // 重新加载以恢复原文本
                        return;
                    }

                    const dateKey = getDateKey(currentDate);
                    const tasks = appData.tasks[dateKey] || [];

                    const task = tasks.find(t => t.id === taskId);
                    if (task) {
                        task.text = newText;
                        saveData();
                        showNotification('任务已更新', 'success');
                    }
                };

                // 更新任务类别
                const updateTaskCategory = (taskId, newCategory) => {
                    const dateKey = getDateKey(currentDate);
                    const tasks = appData.tasks[dateKey] || [];

                    const task = tasks.find(t => t.id === taskId);
                    if (task) {
                        task.category = newCategory;
                        saveData();
                        loadTasks(); // 重新加载以更新样式
                        showNotification('任务类别已更新', 'success');
                    }
                };

                // 删除任务
                const deleteTask = (taskId) => {
                    const dateKey = getDateKey(currentDate);
                    if (appData.tasks[dateKey]) {
                        appData.tasks[dateKey] = appData.tasks[dateKey].filter(t => t.id !== taskId);
                        saveData();
                        loadTasks();
                        updateCompletionRateChart(); // 更新图表
                        showNotification('任务已删除', 'success');
                    }
                };

                // 更新任务统计
                const updateTaskStats = () => {
                    const dateKey = getDateKey(currentDate);
                    const tasks = appData.tasks[dateKey] || [];
                    const coreTasks = appData.coreTasks[dateKey] || [];

                    // 核心计划统计
                    const coreTotal = coreTasks.length;
                    const coreCompleted = coreTasks.filter(t => t.completed).length;

                    // 今日计划统计
                    const dailyTotal = tasks.length;
                    const dailyCompleted = tasks.filter(t => t.completed).length;

                    // 总计划统计
                    const total = coreTotal + dailyTotal;
                    const completed = coreCompleted + dailyCompleted;
                    const pending = total - completed;
                    const progress = total > 0 ? Math.round((completed / total) * 100) : 0;

                    // 更新进度环
                    if (elements.progressRing) {
                        const circumference = 2 * Math.PI * 16; // 圆周长
                        const offset = circumference - (progress / 100) * circumference;
                        elements.progressRing.style.strokeDasharray = `${circumference} ${circumference}`;
                        elements.progressRing.style.strokeDashoffset = offset;
                    }

                    // 更新统计数字
                    if (elements.progressPercent) elements.progressPercent.textContent = `${progress}%`;

                    // 核心计划统计
                    if (elements.coreTotalCount) elements.coreTotalCount.textContent = coreTotal;
                    if (elements.coreCompletedCount) elements.coreCompletedCount.textContent = coreCompleted;
                    if (elements.coreTasksDisplay) elements.coreTasksDisplay.textContent = `${coreCompleted}/${coreTotal}`;

                    // 今日计划统计
                    if (elements.totalCount) elements.totalCount.textContent = dailyTotal;
                    if (elements.completedCount) elements.completedCount.textContent = dailyCompleted;
                    if (elements.dailyTasksDisplay) elements.dailyTasksDisplay.textContent = `${dailyCompleted}/${dailyTotal}`;

                    // 总计划统计
                    if (elements.totalTasks) elements.totalTasks.textContent = total;
                    if (elements.finishedTasks) elements.finishedTasks.textContent = completed;
                    if (elements.pendingTasks) elements.pendingTasks.textContent = pending;

                    // 更新评分显示
                    updateRatingDisplay();
                };

                // 新增：创建改进项元素
                const createImprovementElement = (improvement, index, totalImprovements, currentDateKey) => {
                    const improvementEl = document.createElement('div');

                    // 获取当前日期的完成状态
                    const isCompleted = improvement.completedDates && improvement.completedDates[currentDateKey] === true;

                    improvementEl.className = `flex items-center p-3 border border-gray-100 rounded-lg hover:bg-gray-50 ${isCompleted ? 'bg-gray-50' : ''}`;
                    improvementEl.dataset.improvementId = improvement.id;

                    // 格式化添加时间
                    const addedDate = improvement.addedDate || new Date().toISOString().split('T')[0];
                    const formattedDate = new Date(addedDate).toLocaleDateString('zh-CN', {
                        month: 'short',
                        day: 'numeric'
                    });

                    improvementEl.innerHTML = `
                        <input type="checkbox" class="improvement-checkbox mr-3 w-4 h-4 text-improvement border-gray-300 rounded focus:ring-improvement" ${isCompleted ? 'checked' : ''}>
                        <span class="flex-1 ${isCompleted ? 'line-through text-gray-400' : ''}">
                            <span class="inline-block w-2 h-2 rounded-full bg-improvement mr-2"></span>
                            <span class="improvement-text cursor-pointer hover:bg-gray-100 px-1 rounded" contenteditable="false">${improvement.text}</span>
                            <span class="text-xs text-gray-400 ml-2">${formattedDate}</span>
                        </span>
                        <div class="flex items-center space-x-1 ml-auto">
                            <button class="edit-improvement text-blue-400 hover:text-blue-600 p-1" title="编辑改进项">
                                <i class="fa fa-edit"></i>
                            </button>
                            <button class="move-improvement-up text-gray-400 hover:text-improvement p-1 ${index === 0 ? 'opacity-30 cursor-not-allowed' : ''}" title="上移">
                                <i class="fa fa-chevron-up"></i>
                            </button>
                            <button class="move-improvement-down text-gray-400 hover:text-improvement p-1 ${index === totalImprovements - 1 ? 'opacity-30 cursor-not-allowed' : ''}" title="下移">
                                <i class="fa fa-chevron-down"></i>
                            </button>
                            <button class="delete-improvement text-gray-400 hover:text-red-500 p-1" title="删除改进项">
                                <i class="fa fa-trash-o"></i>
                            </button>
                        </div>
                    `;

                    // 添加事件监听
                    const checkbox = improvementEl.querySelector('.improvement-checkbox');
                    if (checkbox) {
                        checkbox.addEventListener('change', () => {
                            toggleImprovementCompletion(improvement.id);
                        });
                    }

                    const editBtn = improvementEl.querySelector('.edit-improvement');
                    const textSpan = improvementEl.querySelector('.improvement-text');
                    if (editBtn && textSpan) {
                        editBtn.addEventListener('click', () => {
                            if (textSpan.contentEditable === 'false') {
                                // 进入编辑模式
                                textSpan.contentEditable = 'true';
                                textSpan.focus();
                                textSpan.style.backgroundColor = '#f3f4f6';
                                editBtn.innerHTML = '<i class="fa fa-check"></i>';
                                editBtn.title = '保存';
                            } else {
                                // 保存编辑
                                const newText = textSpan.textContent.trim();
                                updateImprovementText(improvement.id, newText);
                                textSpan.contentEditable = 'false';
                                textSpan.style.backgroundColor = '';
                                editBtn.innerHTML = '<i class="fa fa-edit"></i>';
                                editBtn.title = '编辑改进项';
                            }
                        });

                        // 按Enter键保存
                        textSpan.addEventListener('keypress', (e) => {
                            if (e.key === 'Enter') {
                                e.preventDefault();
                                editBtn.click();
                            }
                        });
                    }

                    const deleteBtn = improvementEl.querySelector('.delete-improvement');
                    if (deleteBtn) {
                        deleteBtn.addEventListener('click', () => {
                            deleteImprovement(improvement.id);
                        });
                    }

                    const moveUpBtn = improvementEl.querySelector('.move-improvement-up');
                    if (moveUpBtn && index > 0) {
                        moveUpBtn.addEventListener('click', () => {
                            moveImprovement(improvement.id, -1); // 上移
                        });
                    }

                    const moveDownBtn = improvementEl.querySelector('.move-improvement-down');
                    if (moveDownBtn && index < totalImprovements - 1) {
                        moveDownBtn.addEventListener('click', () => {
                            moveImprovement(improvement.id, 1); // 下移
                        });
                    }

                    return improvementEl;
                };

                // 新增：移动改进项
                const moveImprovement = (improvementId, direction) => {
                    const improvements = appData.improvements || [];
                    const improvementIndex = improvements.findIndex(i => i.id === improvementId);

                    if (improvementIndex === -1) return;

                    // 计算新位置
                    const newIndex = improvementIndex + direction;

                    // 检查边界
                    if (newIndex < 0 || newIndex >= improvements.length) return;

                    // 交换位置
                    [improvements[improvementIndex], improvements[newIndex]] = [improvements[newIndex], improvements[improvementIndex]];

                    saveData();
                    loadImprovements();
                };

                // 当前活动标签
                let currentTab = 'improvement'; // 'improvement' 或 'todo'

                // 标签切换功能
                const switchTab = (tabName) => {
                    currentTab = tabName;
                    const improvementTab = document.getElementById('improvementTab');
                    const todoTab = document.getElementById('todoTab');
                    const improvementSection = document.getElementById('improvementSection');
                    const todoSection = document.getElementById('todoSection');

                    if (tabName === 'improvement') {
                        improvementTab.classList.add('bg-improvement', 'text-white');
                        improvementTab.classList.remove('text-gray-600', 'hover:text-gray-900');
                        todoTab.classList.remove('bg-blue-600', 'text-white');
                        todoTab.classList.add('text-gray-600', 'hover:text-gray-900');
                        improvementSection.classList.remove('hidden');
                        todoSection.classList.add('hidden');
                        loadImprovements();
                    } else {
                        todoTab.classList.add('bg-blue-600', 'text-white');
                        todoTab.classList.remove('text-gray-600', 'hover:text-gray-900');
                        improvementTab.classList.remove('bg-improvement', 'text-white');
                        improvementTab.classList.add('text-gray-600', 'hover:text-gray-900');
                        todoSection.classList.remove('hidden');
                        improvementSection.classList.add('hidden');
                        loadTodos();
                    }
                };

                // 新增：加载改进项列表（每天重置完成状态）
                const loadImprovements = () => {
                    if (!elements.improvementList || !elements.emptyImprovementState) return;

                    const improvements = appData.improvements || [];

                    // 获取当前日期的完成状态
                    const today = getDateKey(currentDate);

                    // 清空列表
                    elements.improvementList.innerHTML = '';

                    // 更新计数
                    const currentTabCount = document.getElementById('currentTabCount');
                    if (currentTabCount && currentTab === 'improvement') {
                        currentTabCount.textContent = improvements.length;
                    }

                    if (improvements.length === 0) {
                        // 显示空状态
                        elements.improvementList.appendChild(elements.emptyImprovementState);
                    } else {
                        // 按添加时间倒序排列（最新的在前面）
                        const sortedImprovements = [...improvements].sort((a, b) => {
                            const dateA = new Date(a.addedDate || '1970-01-01');
                            const dateB = new Date(b.addedDate || '1970-01-01');
                            return dateB - dateA;
                        });

                        // 添加改进项
                        sortedImprovements.forEach((improvement, index) => {
                            const improvementEl = createImprovementElement(improvement, index, sortedImprovements.length, today);
                            elements.improvementList.appendChild(improvementEl);
                        });
                    }
                };

                // 新增：加载待办事项列表
                const loadTodos = () => {
                    if (!elements.todoList || !elements.emptyTodoState) return;

                    const todos = appData.todos || [];

                    // 清空列表
                    elements.todoList.innerHTML = '';

                    // 更新计数
                    const currentTabCount = document.getElementById('currentTabCount');
                    if (currentTabCount && currentTab === 'todo') {
                        currentTabCount.textContent = todos.length;
                    }

                    if (todos.length === 0) {
                        // 显示空状态
                        if (elements.emptyTodoState.parentNode !== elements.todoList) {
                            elements.todoList.appendChild(elements.emptyTodoState);
                        }
                    } else {
                        // 按添加时间倒序排列，未完成的在前面
                        const sortedTodos = [...todos].sort((a, b) => {
                            if (a.completed !== b.completed) {
                                return a.completed ? 1 : -1; // 未完成的在前面
                            }
                            const dateA = new Date(a.addedDate || '1970-01-01');
                            const dateB = new Date(b.addedDate || '1970-01-01');
                            return dateB - dateA;
                        });

                        // 添加待办事项
                        sortedTodos.forEach((todo, index) => {
                            const todoEl = createTodoElement(todo, index, sortedTodos.length);
                            elements.todoList.appendChild(todoEl);
                        });
                    }
                };

                // 新增：切换改进项完成状态（按日期记录）
                const toggleImprovementCompletion = (improvementId) => {
                    const improvements = appData.improvements || [];
                    const improvement = improvements.find(i => i.id === improvementId);

                    if (improvement) {
                        const currentDateKey = getDateKey(currentDate);

                        // 初始化completedDates对象
                        if (!improvement.completedDates) {
                            improvement.completedDates = {};
                        }

                        // 切换当前日期的完成状态
                        improvement.completedDates[currentDateKey] = !improvement.completedDates[currentDateKey];

                        saveData();
                        loadImprovements();
                    }
                };

                // 新增：更新改进项文本
                const updateImprovementText = (improvementId, newText) => {
                    if (!newText.trim()) {
                        showNotification('改进项内容不能为空', 'error');
                        loadImprovements(); // 重新加载以恢复原文本
                        return;
                    }

                    const improvements = appData.improvements || [];
                    const improvement = improvements.find(i => i.id === improvementId);

                    if (improvement) {
                        improvement.text = newText;
                        saveData();
                        showNotification('改进项已更新', 'success');
                    }
                };

                // 新增：添加新改进项
                const addImprovement = () => {
                    if (!elements.newImprovementInput) return;

                    const improvementText = elements.newImprovementInput.value.trim();
                    if (!improvementText) {
                        showNotification('请输入改进内容', 'error');
                        return;
                    }

                    if (!appData.improvements) {
                        appData.improvements = [];
                    }

                    const newImprovement = {
                        id: Date.now().toString(),
                        text: improvementText,
                        completed: false,
                        addedDate: new Date().toISOString().split('T')[0] // 添加当前日期
                    };

                    appData.improvements.push(newImprovement);
                    saveData();
                    loadImprovements();

                    // 清空输入框
                    elements.newImprovementInput.value = '';
                    elements.newImprovementInput.focus();
                    showNotification('改进项添加成功', 'success');
                };

                // 新增：删除改进项
                const deleteImprovement = (improvementId) => {
                    if (appData.improvements) {
                        appData.improvements = appData.improvements.filter(i => i.id !== improvementId);
                        saveData();
                        loadImprovements();
                        showNotification('改进项已删除', 'success');
                    }
                };

                // 新增：创建待办事项元素（与每日计划样式保持一致）
                const createTodoElement = (todo, index, total) => {
                    const todoEl = document.createElement('div');
                    todoEl.className = 'flex items-center p-3 border border-gray-100 rounded-lg hover:bg-gray-50';
                    todoEl.dataset.todoId = todo.id;

                    todoEl.innerHTML = `
                        <input type="checkbox" class="todo-checkbox w-5 h-5 rounded text-blue-600 focus:ring-blue-500" ${todo.completed ? 'checked' : ''}>
                        <div class="ml-2 flex-1 ${todo.completed ? 'task-complete' : ''}">
                            <span class="inline-block w-2 h-2 rounded-full bg-blue-600 mr-2"></span>
                            <span class="todo-text cursor-pointer hover:bg-gray-100 px-1 rounded" contenteditable="false">${todo.text}</span>
                        </div>
                        <div class="flex items-center space-x-1 ml-auto">
                            <button class="edit-todo text-blue-400 hover:text-blue-600 p-1" title="编辑待办事项">
                                <i class="fa fa-edit"></i>
                            </button>
                            <button class="move-todo-up text-gray-400 hover:text-primary p-1 ${index === 0 ? 'opacity-30 cursor-not-allowed' : ''}" title="上移">
                                <i class="fa fa-chevron-up"></i>
                            </button>
                            <button class="move-todo-down text-gray-400 hover:text-primary p-1 ${index === total - 1 ? 'opacity-30 cursor-not-allowed' : ''}" title="下移">
                                <i class="fa fa-chevron-down"></i>
                            </button>
                            <button class="delete-todo text-gray-400 hover:text-red-500 p-1" title="删除待办事项">
                                <i class="fa fa-trash-o"></i>
                            </button>
                        </div>
                    `;

                    // 添加事件监听
                    const checkbox = todoEl.querySelector('.todo-checkbox');
                    const todoText = todoEl.querySelector('.todo-text');
                    const editBtn = todoEl.querySelector('.edit-todo');
                    const deleteBtn = todoEl.querySelector('.delete-todo');
                    const moveUpBtn = todoEl.querySelector('.move-todo-up');
                    const moveDownBtn = todoEl.querySelector('.move-todo-down');

                    if (checkbox) {
                        checkbox.addEventListener('change', () => {
                            toggleTodoCompletion(todo.id);
                        });
                    }

                    // 编辑待办事项文本
                    if (editBtn && todoText) {
                        editBtn.addEventListener('click', () => {
                            if (todoText.contentEditable === 'false') {
                                todoText.contentEditable = 'true';
                                todoText.focus();
                                todoText.style.backgroundColor = '#FEF3C7';
                                editBtn.innerHTML = '<i class="fa fa-check"></i>';
                                editBtn.title = '保存';
                            } else {
                                todoText.contentEditable = 'false';
                                todoText.style.backgroundColor = '';
                                editBtn.innerHTML = '<i class="fa fa-edit"></i>';
                                editBtn.title = '编辑待办事项';
                                updateTodoText(todo.id, todoText.textContent.trim());
                            }
                        });

                        // 按Enter键保存编辑
                        todoText.addEventListener('keydown', (e) => {
                            if (e.key === 'Enter') {
                                e.preventDefault();
                                todoText.contentEditable = 'false';
                                todoText.style.backgroundColor = '';
                                editBtn.innerHTML = '<i class="fa fa-edit"></i>';
                                editBtn.title = '编辑待办事项';
                                updateTodoText(todo.id, todoText.textContent.trim());
                            }
                        });
                    }

                    if (deleteBtn) {
                        deleteBtn.addEventListener('click', () => {
                            if (confirm('确定要删除这个待办事项吗？')) {
                                deleteTodo(todo.id);
                            }
                        });
                    }

                    if (moveUpBtn && index > 0) {
                        moveUpBtn.addEventListener('click', () => {
                            moveTodo(todo.id, -1); // 上移
                        });
                    }

                    if (moveDownBtn && index < total - 1) {
                        moveDownBtn.addEventListener('click', () => {
                            moveTodo(todo.id, 1); // 下移
                        });
                    }

                    return todoEl;
                };

                // 新增：切换待办事项完成状态
                const toggleTodoCompletion = (todoId) => {
                    const todos = appData.todos || [];
                    const todo = todos.find(t => t.id === todoId);

                    if (todo) {
                        todo.completed = !todo.completed;
                        if (todo.completed) {
                            todo.completedDate = new Date().toISOString().split('T')[0];
                        } else {
                            delete todo.completedDate;
                        }

                        // 立即更新UI
                        const todoElement = document.querySelector(`[data-todo-id="${todoId}"]`);
                        if (todoElement) {
                            const todoDiv = todoElement.querySelector('.ml-2');
                            if (todo.completed) {
                                todoDiv.classList.add('task-complete');
                            } else {
                                todoDiv.classList.remove('task-complete');
                            }
                        }

                        saveData();
                        // 重新加载以更新排序（已完成的排到后面）
                        loadTodos();
                    }
                };

                // 暴露到全局作用域
                window.toggleTodoCompletion = toggleTodoCompletion;



                // 新增：更新待办事项文本
                const updateTodoText = (todoId, newText) => {
                    if (!newText.trim()) {
                        showNotification('待办事项内容不能为空', 'error');
                        loadTodos(); // 重新加载以恢复原文本
                        return;
                    }

                    const todos = appData.todos || [];
                    const todo = todos.find(t => t.id === todoId);

                    if (todo) {
                        todo.text = newText.trim();
                        saveData();
                        loadTodos();
                        showNotification('待办事项已更新', 'success');
                    }
                };

                // 新增：移动待办事项
                const moveTodo = (todoId, direction) => {
                    const todos = appData.todos || [];
                    const todoIndex = todos.findIndex(t => t.id === todoId);

                    if (todoIndex === -1) return;

                    const newIndex = todoIndex + direction;

                    if (newIndex < 0 || newIndex >= todos.length) return;

                    // 交换位置
                    [todos[todoIndex], todos[newIndex]] = [todos[newIndex], todos[todoIndex]];

                    saveData();
                    loadTodos();
                };

                // 新增：添加待办事项
                const addTodo = () => {
                    if (!elements.newTodoInput) return;

                    const text = elements.newTodoInput.value.trim();
                    if (!text) {
                        showNotification('请输入待办事项内容', 'error');
                        return;
                    }

                    const newTodo = {
                        id: Date.now().toString(),
                        text: text,
                        completed: false,
                        addedDate: new Date().toISOString().split('T')[0]
                    };

                    appData.todos.push(newTodo);
                    saveData();

                    // 立即更新显示
                    loadTodos();

                    // 清空输入框
                    elements.newTodoInput.value = '';
                    elements.newTodoInput.focus();
                    showNotification('待办事项添加成功', 'success');
                };

                // 新增：删除待办事项
                const deleteTodo = (todoId) => {
                    if (appData.todos) {
                        appData.todos = appData.todos.filter(t => t.id !== todoId);
                        saveData();

                        // 立即更新显示
                        loadTodos();

                        showNotification('待办事项已删除', 'success');
                    }
                };

                // 暴露待办事项函数到全局作用域
                window.moveTodo = moveTodo;
                window.deleteTodo = deleteTodo;

                // 设置评分
                const setRating = (rating) => {
                    const dateKey = getDateKey(currentDate);
                    appData.ratings[dateKey] = rating;
                    saveData();
                    updateRatingDisplay();
                    showNotification('评分已保存', 'success');
                };

                // 更新评分显示
                const updateRatingDisplay = () => {
                    if (!elements.ratingStars || !elements.ratingText) return;
                    
                    const dateKey = getDateKey(currentDate);
                    const rating = appData.ratings[dateKey] || 0;
                    
                    // 更新星星显示
                    const stars = elements.ratingStars.querySelectorAll('i');
                    stars.forEach((star, index) => {
                        if (index + 1 <= rating) {
                            star.classList.remove('text-gray-300');
                            star.classList.add('text-yellow-400');
                        } else {
                            star.classList.remove('text-yellow-400');
                            star.classList.add('text-gray-300');
                        }
                    });
                    
                    // 更新评分文本
                    const ratingTexts = ['未评分', '很差', '一般', '良好', '优秀', '完美'];
                    elements.ratingText.textContent = ratingTexts[rating];
                };

                // 创建感想卡片
                const createReflectionCard = (reflection) => {
                    const card = document.createElement('div');
                    card.className = 'reflection-card';
                    card.dataset.reflectionId = reflection.id;
                    
                    // 格式化时间
                    const date = new Date(reflection.timestamp);
                    const timeString = date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
                    
                    card.innerHTML = `
                        <div class="flex justify-between items-start mb-2">
                            <p class="text-sm text-gray-500">${timeString}</p>
                            <div class="flex space-x-1">
                                <button class="edit-reflection text-gray-400 hover:text-primary p-1">
                                    <i class="fa fa-pencil"></i>
                                </button>
                                <button class="delete-reflection text-gray-400 hover:text-red-500 p-1">
                                    <i class="fa fa-trash-o"></i>
                                </button>
                            </div>
                        </div>
                        <p class="text-gray-800 mb-2">${reflection.text}</p>
                        
                        <!-- 编辑区域 (默认隐藏) -->
                        <div class="edit-reflection-container hidden mt-2">
                            <textarea class="w-full p-2 border border-gray-200 rounded text-sm" rows="2">${reflection.text}</textarea>
                            <div class="flex justify-end space-x-2 mt-2">
                                <button class="cancel-edit text-gray-500 px-2 py-1 rounded text-xs">取消</button>
                                <button class="save-edit bg-primary text-white px-2 py-1 rounded text-xs">保存</button>
                            </div>
                            <p class="edit-reflection-message text-xs mt-1 hidden"></p>
                        </div>
                    `;
                    
                    // 添加事件监听
                    const editBtn = card.querySelector('.edit-reflection');
                    const deleteBtn = card.querySelector('.delete-reflection');
                    const cancelBtn = card.querySelector('.cancel-edit');
                    const saveBtn = card.querySelector('.save-edit');
                    const messageEl = card.querySelector('.edit-reflection-message');
                    const cardContent = card.querySelector('p.text-gray-800');
                    const editContainer = card.querySelector('.edit-reflection-container');
                    const editTextarea = card.querySelector('.edit-reflection-container textarea');
                    
                    if (editBtn && cardContent && editContainer) {
                        editBtn.addEventListener('click', () => {
                            cardContent.classList.add('hidden');
                            editContainer.classList.remove('hidden');
                            if (editTextarea) editTextarea.focus();
                            if (messageEl) messageEl.classList.add('hidden');
                        });
                    }
                    
                    if (cancelBtn && cardContent && editContainer) {
                        cancelBtn.addEventListener('click', () => {
                            cardContent.classList.remove('hidden');
                            editContainer.classList.add('hidden');
                            if (messageEl) messageEl.classList.add('hidden');
                        });
                    }
                    
                    if (saveBtn && editTextarea && messageEl) {
                        saveBtn.addEventListener('click', () => {
                            const newText = editTextarea.value.trim();
                            if (!newText) {
                                messageEl.textContent = '内容不能为空';
                                messageEl.classList.remove('hidden', 'text-green-500');
                                messageEl.classList.add('text-red-500');
                                return;
                            }
                            
                            updateReflection(reflection.id, newText);
                            messageEl.textContent = '修改成功';
                            messageEl.classList.remove('hidden', 'text-red-500');
                            messageEl.classList.add('text-green-500');
                            
                            // 2秒后隐藏编辑区域
                            setTimeout(() => {
                                cardContent.classList.remove('hidden');
                                editContainer.classList.add('hidden');
                            }, 1000);
                        });
                    }
                    
                    if (deleteBtn) {
                        deleteBtn.addEventListener('click', () => {
                            if (confirm('确定要删除这条感想吗？')) {
                                deleteReflection(reflection.id);
                            }
                        });
                    }
                    
                    return card;
                };

                // 加载感想列表
                const loadReflections = () => {
                    if (!elements.reflectionsContainer || !elements.noReflections) return;
                    
                    const dateKey = getDateKey(currentDate);
                    const reflections = appData.reflections[dateKey] || [];
                    
                    // 清空容器
                    elements.reflectionsContainer.innerHTML = '';
                    
                    if (reflections.length === 0) {
                        // 显示无感想状态
                        elements.reflectionsContainer.appendChild(elements.noReflections);
                    } else {
                        // 按时间排序，最新的在前面
                        const sortedReflections = [...reflections].sort((a, b) => b.timestamp - a.timestamp);
                        
                        // 添加感想卡片
                        sortedReflections.forEach(reflection => {
                            const card = createReflectionCard(reflection);
                            elements.reflectionsContainer.appendChild(card);
                        });
                    }
                };

                // 添加新感想
                const addReflection = () => {
                    if (!elements.newReflectionInput) return;
                    
                    const text = elements.newReflectionInput.value.trim();
                    if (!text) {
                        showNotification('请输入感想内容', 'error');
                        return;
                    }
                    
                    const dateKey = getDateKey(currentDate);
                    if (!appData.reflections[dateKey]) {
                        appData.reflections[dateKey] = [];
                    }
                    
                    const newReflection = {
                        id: Date.now().toString(),
                        text: text,
                        timestamp: Date.now()
                    };
                    
                    appData.reflections[dateKey].push(newReflection);
                    saveData();
                    loadReflections();
                    
                    // 清空输入框
                    elements.newReflectionInput.value = '';
                    elements.newReflectionInput.focus();
                    showNotification('感想添加成功', 'success');
                };

                // 更新感想
                const updateReflection = (reflectionId, newText) => {
                    const dateKey = getDateKey(currentDate);
                    const reflections = appData.reflections[dateKey] || [];
                    
                    const reflection = reflections.find(r => r.id === reflectionId);
                    if (reflection) {
                        reflection.text = newText;
                        reflection.timestamp = Date.now(); // 更新时间戳
                        saveData();
                        loadReflections();
                    }
                };

                // 删除感想
                const deleteReflection = (reflectionId) => {
                    const dateKey = getDateKey(currentDate);
                    if (appData.reflections[dateKey]) {
                        appData.reflections[dateKey] = appData.reflections[dateKey].filter(r => r.id !== reflectionId);
                        saveData();
                        loadReflections();
                        showNotification('感想已删除', 'success');
                    }
                };

                // 切换日期
                const changeDate = (days) => {
                    // 保存当前日期的数据到历史
                    archiveCurrentDate();

                    currentDate.setDate(currentDate.getDate() + days);
                    updateDisplayDate();
                    updateHolidayUI(); // 新增：更新假期UI
                    loadCoreTasks(); // 新增：加载核心任务
                    loadTasks();
                    if (currentTab === 'improvement') loadImprovements(); // 新增：加载改进项（每天重置）
                    else loadTodos(); // 加载待办事项
                    loadReflections();
                    updateRatingDisplay();
                    updateCompletionRateChart(); // 更新图表
                };

                // 切换到指定日期
                const goToDate = (year, month, day) => {
                    // 保存当前日期的数据到历史
                    archiveCurrentDate();

                    currentDate = new Date(year, month, day);
                    if (elements.calendarModal) {
                        elements.calendarModal.classList.add('hidden');
                    }
                    updateDisplayDate();
                    updateHolidayUI(); // 新增：更新假期UI
                    loadCoreTasks(); // 新增：加载核心任务
                    loadTasks();
                    if (currentTab === 'improvement') loadImprovements(); // 新增：加载改进项（每天重置）
                    else loadTodos(); // 加载待办事项
                    loadReflections();
                    updateRatingDisplay();
                    updateCompletionRateChart(); // 更新图表
                };

                // 生成日历
                const generateCalendar = () => {
                    if (!elements.calendarDays || !elements.currentMonth) return;
                    
                    const year = calendarDate.getFullYear();
                    const month = calendarDate.getMonth();
                    
                    // 更新月份显示
                    elements.currentMonth.textContent = formatMonth(calendarDate);
                    
                    // 获取当月第一天是星期几
                    const firstDay = new Date(year, month, 1).getDay();
                    
                    // 获取当月的天数
                    const daysInMonth = new Date(year, month + 1, 0).getDate();
                    
                    // 清空日历
                    elements.calendarDays.innerHTML = '';
                    
                    // 添加空白格子
                    for (let i = 0; i < firstDay; i++) {
                        const emptyDay = document.createElement('div');
                        elements.calendarDays.appendChild(emptyDay);
                    }
                    
                    // 添加日期
                    const today = new Date();
                    const currentDateKey = getDateKey(currentDate);
                    
                    for (let day = 1; day <= daysInMonth; day++) {
                        const dayEl = document.createElement('div');
                        dayEl.className = 'calendar-day';
                        dayEl.textContent = day;
                        
                        // 检查是否是今天
                        const isToday = day === today.getDate() && 
                                       month === today.getMonth() && 
                                       year === today.getFullYear();
                        
                        // 检查是否是当前选中的日期
                        const dateKey = getDateKey(new Date(year, month, day));
                        const isSelected = dateKey === currentDateKey;
                        
                        // 添加相应的样式
                        if (isSelected) {
                            dayEl.classList.add('calendar-day-selected');
                        } else if (isToday) {
                            dayEl.classList.add('calendar-day-today');
                        }
                        
                        // 添加点击事件
                        dayEl.addEventListener('click', () => {
                            goToDate(year, month, day);
                        });
                        
                        elements.calendarDays.appendChild(dayEl);
                    }
                };

                // 切换月份
                const changeMonth = (months) => {
                    calendarDate.setMonth(calendarDate.getMonth() + months);
                    generateCalendar();
                };

                // 将当前日期数据存档到历史
                const archiveCurrentDate = () => {
                    const today = new Date();
                    today.setHours(0, 0, 0, 0);

                    const current = new Date(currentDate);
                    current.setHours(0, 0, 0, 0);

                    // 只存档过去的日期
                    if (current < today) {
                        const dateKey = getDateKey(currentDate);
                        appData.history[dateKey] = {
                            tasks: [...(appData.tasks[dateKey] || [])],
                            coreTasks: [...(appData.coreTasks[dateKey] || [])], // 新增：存档核心任务
                            reflections: [...(appData.reflections[dateKey] || [])],
                            rating: appData.ratings[dateKey] || 0
                        };
                        saveData();
                    }
                    // 无论是否存档，都更新图表以确保实时显示
                    updateCompletionRateChart();
                };

                // 显示历史记录
                const showHistory = () => {
                    if (!elements.historyContent || !elements.historyModal) return;
                    
                    const historyDates = Object.keys(appData.history).sort((a, b) => new Date(b) - new Date(a));
                    
                    if (historyDates.length === 0) {
                        elements.historyContent.innerHTML = `
                            <div class="text-center py-10 text-gray-400">
                                <i class="fa fa-folder-open-o text-3xl mb-2"></i>
                                <p>暂无历史记录</p>
                            </div>
                        `;
                    } else {
                        elements.historyContent.innerHTML = '';
                        
                        historyDates.forEach(dateKey => {
                            const historyItem = createHistoryItem(dateKey);
                            elements.historyContent.appendChild(historyItem);
                        });
                    }
                    
                    elements.historyModal.classList.remove('hidden');
                };

                // 创建历史记录项
                const createHistoryItem = (dateKey) => {
                    const history = appData.history[dateKey];
                    const date = new Date(dateKey);
                    const completedTasks = history.tasks.filter(t => t.completed).length;
                    const totalTasks = history.tasks.length;
                    const completionRate = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;
                    
                    // 评分文本
                    const ratingTexts = ['未评分', '很差', '一般', '良好', '优秀', '完美'];
                    const ratingText = ratingTexts[history.rating];
                    
                    const item = document.createElement('div');
                    item.className = 'border border-gray-100 rounded-lg p-3 mb-3 hover:bg-gray-50';
                    
                    item.innerHTML = `
                        <div class="flex justify-between items-center mb-2">
                            <h4 class="font-medium">${formatDate(date)}</h4>
                            <div class="flex items-center">
                                <span class="text-yellow-400 mr-1">
                                    ${Array(history.rating).fill('<i class="fa fa-star text-xs"></i>').join('')}
                                </span>
                                <span class="text-xs text-gray-500">${ratingText}</span>
                            </div>
                        </div>
                        <div class="text-sm text-gray-600 mb-2">
                            完成 ${completedTasks}/${totalTasks} 项任务 (${completionRate}%)
                        </div>
                        <div class="text-sm text-gray-600 mb-2">
                            ${history.improvements.length} 项改进建议
                        </div>
                        <div class="text-sm text-gray-600 mb-3">
                            ${history.reflections.length} 条感想
                        </div>
                        <button class="view-history-item text-primary text-sm">
                            <i class="fa fa-eye mr-1"></i> 查看详情
                        </button>
                    `;
                    
                    const viewBtn = item.querySelector('.view-history-item');
                    if (viewBtn && elements.historyModal) {
                        viewBtn.addEventListener('click', () => {
                            elements.historyModal.classList.add('hidden');
                            viewHistoryItem(dateKey);
                        });
                    }
                    
                    return item;
                };

                // 查看历史记录详情
                const viewHistoryItem = (dateKey) => {
                    // 切换到该日期
                    const [year, month, day] = dateKey.split('-').map(Number);
                    currentDate = new Date(year, month - 1, day);
                    
                    // 加载数据
                    loadTasks();
                    loadImprovements(); // 新增：加载改进项
                    loadReflections();
                    updateRatingDisplay();
                    updateDisplayDate();
                };

                // 清除历史记录
                const clearHistory = () => {
                    if (confirm('确定要清除所有历史记录吗？此操作无法撤销。')) {
                        appData.history = {};
                        saveData();
                        showHistory();
                        updateCompletionRateChart(); // 更新图表
                        showNotification('历史记录已清除', 'success');
                    }
                };

                // 导出数据
                const exportData = () => {
                    try {
                        // 创建要导出的数据
                        const exportData = {
                            timestamp: new Date().toISOString(),
                            data: appData
                        };
                        
                        // 转换为JSON字符串
                        const jsonString = JSON.stringify(exportData, null, 2);
                        
                        // 创建Blob对象
                        const blob = new Blob([jsonString], { type: 'application/json' });
                        
                        // 创建下载链接
                        const url = URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = `daily-planner-data-${new Date().toISOString().split('T')[0]}.json`;
                        document.body.appendChild(a);
                        
                        // 触发下载
                        a.click();
                        
                        // 清理
                        document.body.removeChild(a);
                        URL.revokeObjectURL(url);
                        
                        // 显示成功消息
                        showNotification('数据导出成功', 'success');
                    } catch (e) {
                        console.error('数据导出失败:', e);
                        showNotification('数据导出失败，请重试', 'error');
                    }
                };

                // 导入数据
                const importData = (file) => {
                    const reader = new FileReader();
                    
                    reader.onload = (e) => {
                        try {
                            const importedData = JSON.parse(e.target.result);
                            
                            // 验证导入的数据格式
                            if (!importedData.data || 
                                typeof importedData.data !== 'object' ||
                                !('tasks' in importedData.data) ||
                                !('improvements' in importedData.data) || // 新增：验证改进项数据
                                !('reflections' in importedData.data) ||
                                !('ratings' in importedData.data) ||
                                !('history' in importedData.data)) {
                                
                                showNotification('导入失败：无效的数据格式', 'error');
                                return;
                            }
                            
                            // 确认是否覆盖现有数据
                            if (confirm('确定要导入数据吗？这将覆盖当前所有数据。')) {
                                appData = importedData.data;
                                saveData();
                                
                                // 重新加载当前视图
                                loadTasks();
                                loadImprovements(); // 新增：加载改进项
                                loadReflections();
                                updateRatingDisplay();
                                updateCompletionRateChart(); // 更新图表
                                
                                showNotification('数据导入成功', 'success');
                            }
                        } catch (error) {
                            console.error('导入数据解析失败:', error);
                            showNotification('导入失败：数据解析错误', 'error');
                        }
                    };
                    
                    reader.onerror = () => {
                        console.error('文件读取错误');
                        showNotification('导入失败：文件读取错误', 'error');
                    };
                    
                    reader.readAsText(file);
                };

                // 保存数据到本地存储
                const saveData = () => {
                    try {
                        localStorage.setItem('dailyPlanner', JSON.stringify(appData));
                    } catch (e) {
                        console.error('保存数据失败:', e);
                        showNotification('保存数据失败，请检查浏览器存储设置', 'error');
                    }
                };

                // 准备完成率图表数据
                const prepareCompletionRateData = () => {
                    // 获取所有历史记录日期
                    const historyDates = Object.keys(appData.history);

                    // 获取所有有任务数据的日期（包括当前日期）
                    const allTaskDates = Object.keys(appData.tasks);
                    const allCoreTaskDates = Object.keys(appData.coreTasks);

                    // 获取所有假期日期
                    const holidayDates = Object.keys(appData.holidays || {});

                    // 合并所有日期并去重
                    const allDates = [...new Set([...historyDates, ...allTaskDates, ...allCoreTaskDates, ...holidayDates])];

                    // 如果没有任何数据
                    if (allDates.length === 0) {
                        return null;
                    }

                    // 按日期排序
                    allDates.sort((a, b) => new Date(a) - new Date(b));

                    // 准备标签和数据
                    const labels = [];
                    const completionRates = [];
                    const holidayFlags = []; // 标记哪些点是假期

                    allDates.forEach(dateKey => {
                        // 检查是否为假期
                        const isHolidayDate = isHoliday(dateKey);

                        if (isHolidayDate) {
                            // 假期显示为特殊标记，不计入完成率统计
                            const date = new Date(dateKey);
                            const today = new Date();
                            today.setHours(0, 0, 0, 0);
                            const targetDate = new Date(date);
                            targetDate.setHours(0, 0, 0, 0);

                            let label;
                            if (targetDate.getTime() === today.getTime()) {
                                label = '今天(假期)';
                            } else {
                                label = `${date.getMonth() + 1}月${date.getDate()}日(假期)`;
                            }

                            labels.push(label);
                            completionRates.push(null); // 假期不显示完成率
                            holidayFlags.push(true);
                            return;
                        }

                        let tasks = [];
                        let coreTasks = [];

                        // 优先使用历史记录中的数据，如果没有则使用当前数据
                        if (appData.history[dateKey]) {
                            tasks = appData.history[dateKey].tasks || [];
                            coreTasks = appData.history[dateKey].coreTasks || [];
                        } else {
                            tasks = appData.tasks[dateKey] || [];
                            coreTasks = appData.coreTasks[dateKey] || [];
                        }

                        // 合并核心任务和普通任务
                        const allTasks = [...coreTasks, ...tasks];

                        // 只显示有任务的日期
                        if (allTasks.length > 0) {
                            const completedTasks = allTasks.filter(t => t.completed).length;
                            const totalTasks = allTasks.length;
                            const completionRate = Math.round((completedTasks / totalTasks) * 100);

                            // 格式化日期标签
                            const date = new Date(dateKey);
                            const today = new Date();
                            today.setHours(0, 0, 0, 0);
                            const targetDate = new Date(date);
                            targetDate.setHours(0, 0, 0, 0);

                            let label;
                            if (targetDate.getTime() === today.getTime()) {
                                label = '今天';
                            } else {
                                label = `${date.getMonth() + 1}月${date.getDate()}日`;
                            }

                            labels.push(label);
                            completionRates.push(completionRate);
                            holidayFlags.push(false);
                        }
                    });

                    return labels.length > 0 ? { labels, completionRates, holidayFlags } : null;
                };

                // 更新任务完成率图表
                const updateCompletionRateChart = () => {
                    if (!elements.completionRateChart || !elements.noHistoryData) return;
                    
                    const chartData = prepareCompletionRateData();
                    
                    // 显示或隐藏"无数据"提示
                    if (!chartData || chartData.labels.length === 0) {
                        if (elements.noHistoryData) {
                            elements.noHistoryData.classList.remove('hidden');
                        }
                        // 如果已有图表实例，销毁它
                        if (completionChart) {
                            completionChart.destroy();
                            completionChart = null;
                        }
                        return;
                    } else {
                        if (elements.noHistoryData) {
                            elements.noHistoryData.classList.add('hidden');
                        }
                    }
                    
                    // 销毁现有图表（如果存在）
                    if (completionChart) {
                        completionChart.destroy();
                    }
                    
                    // 创建新图表
                    const ctx = elements.completionRateChart.getContext('2d');

                    // 为假期和普通日期创建不同的点样式
                    const pointBackgroundColors = chartData.completionRates.map((rate, index) => {
                        if (chartData.holidayFlags && chartData.holidayFlags[index]) {
                            return 'rgba(245, 158, 11, 1)'; // 假期用橙色
                        }
                        return 'rgba(79, 70, 229, 1)'; // 普通日期用蓝色
                    });

                    const pointBorderColors = chartData.completionRates.map((rate, index) => {
                        if (chartData.holidayFlags && chartData.holidayFlags[index]) {
                            return 'rgba(245, 158, 11, 1)'; // 假期用橙色
                        }
                        return 'rgba(79, 70, 229, 1)'; // 普通日期用蓝色
                    });

                    const pointRadii = chartData.completionRates.map((rate, index) => {
                        if (chartData.holidayFlags && chartData.holidayFlags[index]) {
                            return 8; // 假期点更大
                        }
                        return 4; // 普通点
                    });

                    completionChart = new Chart(ctx, {
                        type: 'line',
                        data: {
                            labels: chartData.labels,
                            datasets: [{
                                label: '任务完成率 (%)',
                                data: chartData.completionRates,
                                backgroundColor: 'rgba(79, 70, 229, 0.1)',
                                borderColor: 'rgba(79, 70, 229, 1)',
                                borderWidth: 2,
                                tension: 0.3,
                                fill: true,
                                pointBackgroundColor: pointBackgroundColors,
                                pointBorderColor: pointBorderColors,
                                pointRadius: pointRadii,
                                pointHoverRadius: pointRadii.map(r => r + 2),
                                spanGaps: true // 允许跳过null值（假期）
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    max: 100,
                                    ticks: {
                                        callback: function(value) {
                                            return value + '%';
                                        }
                                    }
                                }
                            },
                            plugins: {
                                tooltip: {
                                    callbacks: {
                                        label: function(context) {
                                            const isHoliday = chartData.holidayFlags && chartData.holidayFlags[context.dataIndex];
                                            if (isHoliday) {
                                                return '假期 🏖️';
                                            }
                                            return `完成率: ${context.raw}%`;
                                        }
                                    }
                                },
                                legend: {
                                    display: false
                                }
                            },
                            interaction: {
                                intersect: false,
                                mode: 'index'
                            }
                        }
                    });
                };

                // 注册事件监听
                const setupEventListeners = () => {
                    // 添加核心任务
                    if (elements.addCoreTaskBtn) {
                        elements.addCoreTaskBtn.addEventListener('click', addCoreTask);
                    }

                    if (elements.newCoreTaskInput) {
                        elements.newCoreTaskInput.addEventListener('keypress', (e) => {
                            if (e.key === 'Enter') addCoreTask();
                        });
                    }

                    // 添加任务
                    if (elements.addTaskBtn) {
                        elements.addTaskBtn.addEventListener('click', addTask);
                    }

                    if (elements.newTaskInput) {
                        elements.newTaskInput.addEventListener('keypress', (e) => {
                            if (e.key === 'Enter') addTask();
                        });
                    }

                    // 新增：标签切换
                    const improvementTab = document.getElementById('improvementTab');
                    const todoTab = document.getElementById('todoTab');
                    if (improvementTab) {
                        improvementTab.addEventListener('click', () => switchTab('improvement'));
                    }
                    if (todoTab) {
                        todoTab.addEventListener('click', () => switchTab('todo'));
                    }

                    // 新增：添加改进项
                    if (elements.addImprovementBtn) {
                        elements.addImprovementBtn.addEventListener('click', addImprovement);
                    }

                    if (elements.newImprovementInput) {
                        elements.newImprovementInput.addEventListener('keypress', (e) => {
                            if (e.key === 'Enter') addImprovement();
                        });
                    }

                    // 新增：添加待办事项
                    if (elements.addTodoBtn) {
                        elements.addTodoBtn.addEventListener('click', addTodo);
                    }
                    if (elements.newTodoInput) {
                        elements.newTodoInput.addEventListener('keypress', (e) => {
                            if (e.key === 'Enter') addTodo();
                        });
                    }

                    // 评分星星点击事件
                    if (elements.ratingStars) {
                        const stars = elements.ratingStars.querySelectorAll('i');
                        stars.forEach((star, index) => {
                            star.addEventListener('click', () => {
                                setRating(index + 1);
                            });
                        });
                    }

                    // 感想相关
                    if (elements.addReflectionBtn) {
                        elements.addReflectionBtn.addEventListener('click', addReflection);
                    }
                    
                    if (elements.newReflectionInput) {
                        elements.newReflectionInput.addEventListener('keypress', (e) => {
                            if (e.key === 'Enter' && !e.shiftKey) {
                                e.preventDefault();
                                addReflection();
                            }
                        });
                    }

                    // 日期导航
                    if (elements.prevDay) {
                        elements.prevDay.addEventListener('click', () => changeDate(-1));
                    }
                    
                    if (elements.nextDay) {
                        elements.nextDay.addEventListener('click', () => changeDate(1));
                    }

                    // 日历相关
                    if (elements.openCalendar) {
                        elements.openCalendar.addEventListener('click', () => {
                            if (elements.calendarModal) {
                                elements.calendarModal.classList.remove('hidden');
                                // 更新日历日期为当前显示的日期
                                calendarDate = new Date(currentDate);
                                generateCalendar();
                            }
                        });
                    }
                    
                    if (elements.closeCalendar) {
                        elements.closeCalendar.addEventListener('click', () => {
                            if (elements.calendarModal) {
                                elements.calendarModal.classList.add('hidden');
                            }
                        });
                    }
                    
                    if (elements.prevMonth) {
                        elements.prevMonth.addEventListener('click', () => changeMonth(-1));
                    }
                    
                    if (elements.nextMonth) {
                        elements.nextMonth.addEventListener('click', () => changeMonth(1));
                    }

                    // 数据导出和导入
                    if (elements.exportDataBtn) {
                        elements.exportDataBtn.addEventListener('click', exportData);
                    }
                    
                    if (elements.importDataBtn) {
                        elements.importDataBtn.addEventListener('change', (e) => {
                            if (e.target.files && e.target.files[0]) {
                                importData(e.target.files[0]);
                                // 重置文件输入，允许重复选择同一个文件
                                e.target.value = '';
                            }
                        });
                    }

                    // 历史记录
                    if (elements.showHistoryBtn) {
                        elements.showHistoryBtn.addEventListener('click', showHistory);
                    }
                    
                    if (elements.closeHistoryBtn && elements.historyModal) {
                        elements.closeHistoryBtn.addEventListener('click', () => {
                            elements.historyModal.classList.add('hidden');
                        });
                    }
                    
                    if (elements.clearHistoryBtn) {
                        elements.clearHistoryBtn.addEventListener('click', clearHistory);
                    }

                    // 点击模态框外部关闭
                    if (elements.historyModal) {
                        elements.historyModal.addEventListener('click', (e) => {
                            if (e.target === elements.historyModal) {
                                elements.historyModal.classList.add('hidden');
                            }
                        });
                    }
                    
                    if (elements.calendarModal) {
                        elements.calendarModal.addEventListener('click', (e) => {
                            if (e.target === elements.calendarModal) {
                                elements.calendarModal.classList.add('hidden');
                            }
                        });
                    }

                    // 假期按钮
                    const toggleHolidayBtn = document.getElementById('toggleHoliday');
                    if (toggleHolidayBtn) {
                        toggleHolidayBtn.addEventListener('click', toggleHoliday);
                    }
                };

                // 假期功能
                const isHoliday = (dateKey) => {
                    return appData.holidays[dateKey] === true;
                };

                const toggleHoliday = () => {
                    const dateKey = getDateKey(currentDate);
                    const isCurrentlyHoliday = isHoliday(dateKey);

                    if (isCurrentlyHoliday) {
                        // 取消假期
                        delete appData.holidays[dateKey];
                        showNotification('已取消假期设置', 'info');
                    } else {
                        // 设为假期，清空当天所有计划
                        if (confirm('设为假期将清空当天所有计划，确定继续吗？')) {
                            appData.holidays[dateKey] = true;

                            // 清空当天的所有计划
                            if (appData.tasks[dateKey]) {
                                delete appData.tasks[dateKey];
                            }
                            if (appData.coreTasks[dateKey]) {
                                delete appData.coreTasks[dateKey];
                            }
                            if (appData.reflections[dateKey]) {
                                delete appData.reflections[dateKey];
                            }
                            if (appData.ratings[dateKey]) {
                                delete appData.ratings[dateKey];
                            }

                            showNotification('已设为假期，当天计划已清空', 'success');
                        } else {
                            return; // 用户取消，不执行任何操作
                        }
                    }

                    saveData();
                    updateHolidayUI();
                    loadCoreTasks();
                    loadTasks();
                    loadReflections();
                    updateRatingDisplay();
                    updateCompletionRateChart();
                };

                const updateHolidayUI = () => {
                    const dateKey = getDateKey(currentDate);
                    const isCurrentlyHoliday = isHoliday(dateKey);
                    const toggleBtn = document.getElementById('toggleHoliday');
                    const buttonText = document.getElementById('holidayButtonText');
                    const indicator = document.getElementById('holidayIndicator');
                    const body = document.body;

                    if (toggleBtn && buttonText && indicator) {
                        if (isCurrentlyHoliday) {
                            toggleBtn.classList.add('holiday-active');
                            buttonText.textContent = '取消假期';
                            indicator.classList.remove('hidden');
                            body.classList.add('holiday-mode');
                        } else {
                            toggleBtn.classList.remove('holiday-active');
                            buttonText.textContent = '设为假期';
                            indicator.classList.add('hidden');
                            body.classList.remove('holiday-mode');
                        }
                    }
                };

                // 检查并自动存档过期数据
                const autoArchiveExpiredData = () => {
                    const today = new Date();
                    today.setHours(0, 0, 0, 0);

                    // 获取所有有数据的日期
                    const allDataDates = [...new Set([
                        ...Object.keys(appData.tasks),
                        ...Object.keys(appData.coreTasks)
                    ])];

                    // 检查所有数据，将过去的日期自动存档
                    allDataDates.forEach(dateKey => {
                        const dataDate = new Date(dateKey);
                        dataDate.setHours(0, 0, 0, 0);

                        // 如果是过去的日期且还没有存档
                        if (dataDate < today && !appData.history[dateKey]) {
                            appData.history[dateKey] = {
                                tasks: [...(appData.tasks[dateKey] || [])],
                                coreTasks: [...(appData.coreTasks[dateKey] || [])],
                                reflections: [...(appData.reflections[dateKey] || [])],
                                rating: appData.ratings[dateKey] || 0
                            };
                            console.log(`自动存档过期数据: ${dateKey}`);
                        }
                    });

                    saveData();
                };

                // 初始化应用
                const initApp = () => {
                    console.log('初始化应用...');

                    // 自动存档过期数据
                    autoArchiveExpiredData();

                    updateDisplayDate();
                    updateHolidayUI(); // 新增：更新假期UI
                    loadCoreTasks(); // 新增：加载核心任务
                    loadTasks();

                    // 初始化标签状态和数据
                    switchTab('improvement'); // 这会加载改进项
                    loadTodos(); // 确保待办事项也被加载

                    loadReflections();
                    updateRatingDisplay();
                    setupEventListeners();
                    updateCompletionRateChart(); // 初始化图表
                    console.log('应用初始化完成');
                };

                // 启动应用
                initApp();
                
            } catch (e) {
                console.error('应用初始化失败:', e);
                alert('应用加载失败，请刷新页面重试。如果问题持续，请检查浏览器设置。');
            }
        });
    </script>

    <!-- 导航栏功能脚本 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 移动端菜单切换
            const mobileMenuButton = document.getElementById('mobile-menu-button');
            const mobileMenu = document.getElementById('mobile-menu');

            if (mobileMenuButton && mobileMenu) {
                mobileMenuButton.addEventListener('click', function() {
                    mobileMenu.classList.toggle('hidden');
                });
            }

            // 滚动时导航栏样式变化（加深阴影）
            window.addEventListener('scroll', function() {
                const nav = document.querySelector('nav');
                if (nav) {
                    if (window.scrollY > 10) {
                        nav.classList.add('shadow-lg');
                    } else {
                        nav.classList.remove('shadow-lg');
                    }
                }
            });
        });
    </script>
</body>
</html>
