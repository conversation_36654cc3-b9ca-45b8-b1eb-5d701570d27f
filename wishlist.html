<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>愿望清单 - 梦想规划师</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <style>
        .card-shadow {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }
        .btn-effect {
            transition: all 0.2s ease-in-out;
        }
        .btn-effect:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }
        .wish-card {
            transition: all 0.3s ease;
        }
        .wish-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
        .completed-wish {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border: 2px solid #0ea5e9;
        }
        .progress-ring {
            transform: rotate(-90deg);
        }
        .image-preview {
            max-height: 200px;
            object-fit: cover;
        }
        .modal {
            backdrop-filter: blur(4px);
        }
        /* 导航栏样式 */
        .nav-link {
            color: #6b7280;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            transition: all 0.2s ease;
            font-weight: 500;
        }
        .nav-link:hover {
            color: #3b82f6;
            background-color: #f3f4f6;
        }
        .nav-link.active {
            color: #3b82f6;
            background-color: #dbeafe;
            font-weight: 600;
        }
        .mobile-nav-link {
            color: #6b7280;
            text-decoration: none;
            transition: all 0.2s ease;
        }
        .mobile-nav-link:hover {
            color: #3b82f6;
            background-color: #f3f4f6;
        }
        .mobile-nav-link.active {
            color: #3b82f6;
            background-color: #dbeafe;
            font-weight: 600;
        }
        .wish-priority-high { border-left: 4px solid #ef4444; }
        .wish-priority-medium { border-left: 4px solid #f59e0b; }
        .wish-priority-low { border-left: 4px solid #10b981; }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 统一导航栏 -->
    <nav class="bg-white shadow-md sticky top-0 left-0 right-0 z-50 transition-all duration-300">
        <div class="container mx-auto px-4">
            <div class="flex justify-between h-16">
                <!-- 左侧Logo/标题 -->
                <div class="flex items-center">
                    <a href="main-plan.html" class="flex items-center">
                        <i class="fa fa-apple text-blue-600 text-2xl mr-2"></i>
                        <span class="font-bold text-xl text-gray-900">生活NOTE</span>
                    </a>
                </div>

                <!-- 右侧导航链接 - 桌面端 -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="main-plan.html" class="nav-link" data-page="main-plan">
                        <i class="fa fa-home mr-2"></i>主计划
                    </a>
                    <a href="expense-tracker.html" class="nav-link" data-page="expense-tracker">
                        <i class="fa fa-money mr-2"></i>支出记录
                    </a>
                    <a href="wishlist.html" class="nav-link active" data-page="wishlist">
                        <i class="fa fa-star mr-2"></i>愿望清单
                    </a>
                    <a href="settings.html" class="nav-link" data-page="settings">
                        <i class="fa fa-cog mr-2"></i>设置
                    </a>
                </div>

                <!-- 移动端菜单按钮 -->
                <div class="md:hidden flex items-center">
                    <button id="mobile-menu-button" class="text-gray-600 hover:text-blue-600 focus:outline-none">
                        <i class="fa fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- 移动端导航菜单 -->
        <div id="mobile-menu" class="hidden md:hidden bg-white shadow-lg w-full">
            <div class="px-2 pt-2 pb-3 space-y-1">
                <a href="main-plan.html" class="mobile-nav-link block px-3 py-2 rounded-md" data-page="main-plan">
                    <i class="fa fa-home mr-2"></i>主计划
                </a>
                <a href="expense-tracker.html" class="mobile-nav-link block px-3 py-2 rounded-md" data-page="expense-tracker">
                    <i class="fa fa-money mr-2"></i>支出记录
                </a>
                <a href="wishlist.html" class="mobile-nav-link active block px-3 py-2 rounded-md" data-page="wishlist">
                    <i class="fa fa-star mr-2"></i>愿望清单
                </a>
                <a href="settings.html" class="mobile-nav-link block px-3 py-2 rounded-md" data-page="settings">
                    <i class="fa fa-cog mr-2"></i>设置
                </a>
            </div>
        </div>
    </nav>

    <!-- 主内容 -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 页面头部 -->
        <div class="mb-8">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                <div>
                    <h2 class="text-3xl font-bold text-gray-900 mb-2">我的愿望清单</h2>
                    <p class="text-gray-600">记录并追踪您的长期目标和梦想</p>
                </div>
                <div class="mt-4 md:mt-0 flex space-x-3">
                    <button id="add-wish-btn" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium btn-effect">
                        <i class="fa fa-plus mr-2"></i>添加愿望
                    </button>
                    <button id="filter-btn" class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg font-medium btn-effect">
                        <i class="fa fa-filter mr-2"></i>筛选
                    </button>
                </div>
            </div>
        </div>

        <!-- 统计卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg p-6 card-shadow">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                        <i class="fa fa-list text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">总愿望</p>
                        <p id="total-wishes" class="text-2xl font-bold text-gray-900">0</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg p-6 card-shadow">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-100 text-green-600">
                        <i class="fa fa-check text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">已实现</p>
                        <p id="completed-wishes" class="text-2xl font-bold text-gray-900">0</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg p-6 card-shadow">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                        <i class="fa fa-clock-o text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">进行中</p>
                        <p id="in-progress-wishes" class="text-2xl font-bold text-gray-900">0</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg p-6 card-shadow">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                        <i class="fa fa-percent text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">完成率</p>
                        <p id="completion-rate" class="text-2xl font-bold text-gray-900">0%</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 筛选栏 -->
        <div id="filter-bar" class="hidden bg-white rounded-lg p-4 card-shadow mb-6">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <select id="status-filter" class="border border-gray-300 rounded-lg px-3 py-2">
                    <option value="">所有状态</option>
                    <option value="planning">计划中</option>
                    <option value="in-progress">进行中</option>
                    <option value="completed">已完成</option>
                </select>
                
                <select id="priority-filter" class="border border-gray-300 rounded-lg px-3 py-2">
                    <option value="">所有优先级</option>
                    <option value="high">高优先级</option>
                    <option value="medium">中优先级</option>
                    <option value="low">低优先级</option>
                </select>
                
                <select id="category-filter" class="border border-gray-300 rounded-lg px-3 py-2">
                    <option value="">所有分类</option>
                    <option value="travel">旅行</option>
                    <option value="career">事业</option>
                    <option value="financial">财务</option>
                    <option value="health">健康</option>
                    <option value="learning">学习</option>
                    <option value="relationship">人际关系</option>
                    <option value="hobby">兴趣爱好</option>
                    <option value="other">其他</option>
                </select>
                
                <button id="clear-filters" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg">
                    清除筛选
                </button>
            </div>
        </div>

        <!-- 愿望列表 -->
        <div id="wishes-container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- 愿望卡片将通过JavaScript动态生成 -->
        </div>

        <!-- 空状态 -->
        <div id="empty-state" class="hidden text-center py-12">
            <i class="fa fa-star text-6xl text-gray-300 mb-4"></i>
            <h3 class="text-xl font-medium text-gray-500 mb-2">还没有愿望</h3>
            <p class="text-gray-400 mb-6">开始记录您的梦想和长期目标吧！</p>
            <button onclick="document.getElementById('add-wish-btn').click()" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium btn-effect">
                <i class="fa fa-plus mr-2"></i>添加第一个愿望
            </button>
        </div>
    </main>

    <!-- 添加/编辑愿望模态框 -->
    <div id="wish-modal" class="hidden fixed inset-0 bg-black bg-opacity-50 modal z-50 flex items-center justify-center p-4">
        <div class="bg-white rounded-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div class="p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 id="modal-title" class="text-2xl font-bold text-gray-900">添加愿望</h3>
                    <button id="close-modal" class="text-gray-400 hover:text-gray-600">
                        <i class="fa fa-times text-xl"></i>
                    </button>
                </div>

                <form id="wish-form" class="space-y-6">
                    <!-- 基本信息 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">愿望标题 *</label>
                        <input type="text" id="wish-title" required class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="例如：去日本旅行">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">详细描述</label>
                        <textarea id="wish-description" rows="4" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="描述您的愿望详情、为什么想要实现它..."></textarea>
                    </div>

                    <!-- 分类和优先级 -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">分类</label>
                            <select id="wish-category" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <option value="travel">旅行</option>
                                <option value="career">事业</option>
                                <option value="financial">财务</option>
                                <option value="health">健康</option>
                                <option value="learning">学习</option>
                                <option value="relationship">人际关系</option>
                                <option value="hobby">兴趣爱好</option>
                                <option value="other">其他</option>
                            </select>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">优先级</label>
                            <select id="wish-priority" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <option value="high">高优先级</option>
                                <option value="medium">中优先级</option>
                                <option value="low">低优先级</option>
                            </select>
                        </div>
                    </div>

                    <!-- 目标日期和预算 -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">目标实现日期</label>
                            <input type="date" id="wish-target-date" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">预估预算 (¥)</label>
                            <input type="number" id="wish-budget" min="0" step="0.01" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="0">
                        </div>
                    </div>

                    <!-- 进度和状态 -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">当前进度 (%)</label>
                            <input type="range" id="wish-progress" min="0" max="100" value="0" class="w-full">
                            <div class="flex justify-between text-xs text-gray-500 mt-1">
                                <span>0%</span>
                                <span id="progress-value">0%</span>
                                <span>100%</span>
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">状态</label>
                            <select id="wish-status" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <option value="planning">计划中</option>
                                <option value="in-progress">进行中</option>
                                <option value="completed">已完成</option>
                            </select>
                        </div>
                    </div>

                    <!-- 图片上传 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">愿望图片</label>
                        <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                            <input type="file" id="wish-images" multiple accept="image/*" class="hidden">
                            <button type="button" onclick="document.getElementById('wish-images').click()" class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg">
                                <i class="fa fa-upload mr-2"></i>选择图片
                            </button>
                            <p class="text-sm text-gray-500 mt-2">支持多张图片，建议尺寸 16:9</p>
                        </div>
                        <div id="image-preview" class="mt-4 grid grid-cols-2 md:grid-cols-3 gap-4"></div>
                    </div>

                    <!-- 步骤规划 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">实现步骤</label>
                        <div id="steps-container" class="space-y-2">
                            <!-- 步骤将动态添加 -->
                        </div>
                        <button type="button" id="add-step" class="mt-2 text-blue-600 hover:text-blue-700 text-sm">
                            <i class="fa fa-plus mr-1"></i>添加步骤
                        </button>
                    </div>

                    <!-- 按钮 -->
                    <div class="flex justify-end space-x-4 pt-6 border-t">
                        <button type="button" id="cancel-btn" class="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                            取消
                        </button>
                        <button type="submit" class="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium">
                            保存愿望
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 愿望详情模态框 -->
    <div id="wish-detail-modal" class="hidden fixed inset-0 bg-black bg-opacity-50 modal z-50 flex items-center justify-center p-4">
        <div class="bg-white rounded-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div id="wish-detail-content">
                <!-- 详情内容将通过JavaScript动态生成 -->
            </div>
        </div>
    </div>

    <!-- 通知组件 -->
    <div id="notification" class="hidden fixed top-4 right-4 z-50 max-w-sm">
        <div class="bg-white rounded-lg shadow-lg border-l-4 p-4">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i id="notification-icon" class="fa text-xl"></i>
                </div>
                <div class="ml-3">
                    <p id="notification-message" class="text-sm font-medium text-gray-900"></p>
                </div>
                <div class="ml-auto pl-3">
                    <button onclick="hideNotification()" class="text-gray-400 hover:text-gray-600">
                        <i class="fa fa-times"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 愿望清单管理类
        class WishlistManager {
            constructor() {
                this.wishes = JSON.parse(localStorage.getItem('wishlist')) || [];
                this.currentEditId = null;
                this.currentFilter = {
                    status: '',
                    priority: '',
                    category: ''
                };
                this.init();
            }

            init() {
                this.setupEventListeners();
                this.renderWishes();
                this.updateStats();
            }

            setupEventListeners() {
                // 移动端菜单
                document.getElementById('mobile-menu-button').addEventListener('click', () => {
                    const menu = document.getElementById('mobile-menu');
                    menu.classList.toggle('hidden');
                });

                // 添加愿望按钮
                document.getElementById('add-wish-btn').addEventListener('click', () => {
                    this.openModal();
                });

                // 筛选按钮
                document.getElementById('filter-btn').addEventListener('click', () => {
                    const filterBar = document.getElementById('filter-bar');
                    filterBar.classList.toggle('hidden');
                });

                // 模态框关闭
                document.getElementById('close-modal').addEventListener('click', () => {
                    this.closeModal();
                });

                document.getElementById('cancel-btn').addEventListener('click', () => {
                    this.closeModal();
                });

                // 表单提交
                document.getElementById('wish-form').addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.saveWish();
                });

                // 进度滑块
                document.getElementById('wish-progress').addEventListener('input', (e) => {
                    document.getElementById('progress-value').textContent = e.target.value + '%';
                });

                // 添加步骤
                document.getElementById('add-step').addEventListener('click', () => {
                    this.addStep();
                });

                // 图片上传
                document.getElementById('wish-images').addEventListener('change', (e) => {
                    this.handleImageUpload(e);
                });

                // 筛选器
                ['status-filter', 'priority-filter', 'category-filter'].forEach(id => {
                    document.getElementById(id).addEventListener('change', () => {
                        this.applyFilters();
                    });
                });

                document.getElementById('clear-filters').addEventListener('click', () => {
                    this.clearFilters();
                });

                // 愿望详情模态框关闭
                document.addEventListener('click', (e) => {
                    if (e.target.id === 'wish-detail-modal') {
                        this.closeDetailModal();
                    }
                });
            }

            openModal(wishId = null) {
                this.currentEditId = wishId;
                const modal = document.getElementById('wish-modal');
                const title = document.getElementById('modal-title');

                if (wishId) {
                    title.textContent = '编辑愿望';
                    this.loadWishData(wishId);
                } else {
                    title.textContent = '添加愿望';
                    this.resetForm();
                }

                modal.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            }

            closeModal() {
                document.getElementById('wish-modal').classList.add('hidden');
                document.body.style.overflow = 'auto';
                this.resetForm();
            }

            closeDetailModal() {
                document.getElementById('wish-detail-modal').classList.add('hidden');
                document.body.style.overflow = 'auto';
            }

            resetForm() {
                document.getElementById('wish-form').reset();
                document.getElementById('progress-value').textContent = '0%';
                document.getElementById('image-preview').innerHTML = '';
                document.getElementById('steps-container').innerHTML = '';
                this.currentEditId = null;
            }

            loadWishData(wishId) {
                const wish = this.wishes.find(w => w.id === wishId);
                if (!wish) return;

                document.getElementById('wish-title').value = wish.title;
                document.getElementById('wish-description').value = wish.description || '';
                document.getElementById('wish-category').value = wish.category;
                document.getElementById('wish-priority').value = wish.priority;
                document.getElementById('wish-target-date').value = wish.targetDate || '';
                document.getElementById('wish-budget').value = wish.budget || '';
                document.getElementById('wish-progress').value = wish.progress || 0;
                document.getElementById('progress-value').textContent = (wish.progress || 0) + '%';
                document.getElementById('wish-status').value = wish.status;

                // 加载图片
                this.displayImages(wish.images || []);

                // 加载步骤
                this.displaySteps(wish.steps || []);
            }

            displayImages(images) {
                const container = document.getElementById('image-preview');
                container.innerHTML = '';

                images.forEach((image, index) => {
                    const div = document.createElement('div');
                    div.className = 'relative';
                    div.innerHTML = `
                        <img src="${image}" alt="愿望图片" class="w-full h-24 object-cover rounded-lg">
                        <button type="button" onclick="wishlistManager.removeImage(${index})" class="absolute top-1 right-1 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs">
                            <i class="fa fa-times"></i>
                        </button>
                    `;
                    container.appendChild(div);
                });
            }

            displaySteps(steps) {
                const container = document.getElementById('steps-container');
                container.innerHTML = '';

                steps.forEach((step, index) => {
                    this.addStep(step, index);
                });
            }

            addStep(stepText = '', index = null) {
                const container = document.getElementById('steps-container');
                const stepIndex = index !== null ? index : container.children.length;

                const div = document.createElement('div');
                div.className = 'flex items-center space-x-2';
                div.innerHTML = `
                    <span class="text-sm text-gray-500">${stepIndex + 1}.</span>
                    <input type="text" value="${stepText}" placeholder="输入实现步骤..." class="flex-1 px-3 py-2 border border-gray-300 rounded-lg text-sm step-input">
                    <button type="button" onclick="this.parentElement.remove()" class="text-red-500 hover:text-red-700">
                        <i class="fa fa-trash text-sm"></i>
                    </button>
                `;
                container.appendChild(div);
            }

            handleImageUpload(event) {
                const files = Array.from(event.target.files);
                const container = document.getElementById('image-preview');

                files.forEach(file => {
                    if (file.type.startsWith('image/')) {
                        const reader = new FileReader();
                        reader.onload = (e) => {
                            const div = document.createElement('div');
                            div.className = 'relative';
                            div.innerHTML = `
                                <img src="${e.target.result}" alt="愿望图片" class="w-full h-24 object-cover rounded-lg">
                                <button type="button" onclick="this.parentElement.remove()" class="absolute top-1 right-1 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs">
                                    <i class="fa fa-times"></i>
                                </button>
                            `;
                            container.appendChild(div);
                        };
                        reader.readAsDataURL(file);
                    }
                });
            }

            removeImage(index) {
                const container = document.getElementById('image-preview');
                const images = container.children;
                if (images[index]) {
                    images[index].remove();
                }
            }

            saveWish() {
                const formData = this.getFormData();

                if (!formData.title.trim()) {
                    this.showNotification('请输入愿望标题', 'error');
                    return;
                }

                if (this.currentEditId) {
                    // 编辑现有愿望
                    const index = this.wishes.findIndex(w => w.id === this.currentEditId);
                    if (index !== -1) {
                        this.wishes[index] = { ...this.wishes[index], ...formData };
                    }
                } else {
                    // 添加新愿望
                    const newWish = {
                        id: Date.now().toString(),
                        createdAt: new Date().toISOString(),
                        ...formData
                    };
                    this.wishes.push(newWish);
                }

                this.saveToStorage();
                this.renderWishes();
                this.updateStats();
                this.closeModal();
                this.showNotification('愿望保存成功', 'success');
            }

            getFormData() {
                // 获取图片
                const imageElements = document.querySelectorAll('#image-preview img');
                const images = Array.from(imageElements).map(img => img.src);

                // 获取步骤
                const stepInputs = document.querySelectorAll('.step-input');
                const steps = Array.from(stepInputs).map(input => input.value.trim()).filter(step => step);

                return {
                    title: document.getElementById('wish-title').value.trim(),
                    description: document.getElementById('wish-description').value.trim(),
                    category: document.getElementById('wish-category').value,
                    priority: document.getElementById('wish-priority').value,
                    targetDate: document.getElementById('wish-target-date').value,
                    budget: parseFloat(document.getElementById('wish-budget').value) || 0,
                    progress: parseInt(document.getElementById('wish-progress').value) || 0,
                    status: document.getElementById('wish-status').value,
                    images: images,
                    steps: steps,
                    updatedAt: new Date().toISOString()
                };
            }

            saveToStorage() {
                localStorage.setItem('wishlist', JSON.stringify(this.wishes));
            }

            renderWishes() {
                const container = document.getElementById('wishes-container');
                const emptyState = document.getElementById('empty-state');

                let filteredWishes = this.getFilteredWishes();

                if (filteredWishes.length === 0) {
                    container.innerHTML = '';
                    emptyState.classList.remove('hidden');
                    return;
                }

                emptyState.classList.add('hidden');
                container.innerHTML = '';

                filteredWishes.forEach(wish => {
                    const wishCard = this.createWishCard(wish);
                    container.appendChild(wishCard);
                });
            }

            getFilteredWishes() {
                return this.wishes.filter(wish => {
                    if (this.currentFilter.status && wish.status !== this.currentFilter.status) return false;
                    if (this.currentFilter.priority && wish.priority !== this.currentFilter.priority) return false;
                    if (this.currentFilter.category && wish.category !== this.currentFilter.category) return false;
                    return true;
                });
            }

            createWishCard(wish) {
                const div = document.createElement('div');
                const isCompleted = wish.status === 'completed';
                const priorityClass = `wish-priority-${wish.priority}`;

                div.className = `wish-card bg-white rounded-xl card-shadow overflow-hidden ${isCompleted ? 'completed-wish' : ''} ${priorityClass}`;

                const categoryIcons = {
                    travel: 'fa-plane',
                    career: 'fa-briefcase',
                    financial: 'fa-money',
                    health: 'fa-heart',
                    learning: 'fa-book',
                    relationship: 'fa-users',
                    hobby: 'fa-gamepad',
                    other: 'fa-star'
                };

                const statusColors = {
                    planning: 'bg-gray-100 text-gray-800',
                    'in-progress': 'bg-yellow-100 text-yellow-800',
                    completed: 'bg-green-100 text-green-800'
                };

                const statusTexts = {
                    planning: '计划中',
                    'in-progress': '进行中',
                    completed: '已完成'
                };

                const priorityColors = {
                    high: 'text-red-600',
                    medium: 'text-yellow-600',
                    low: 'text-green-600'
                };

                const priorityTexts = {
                    high: '高优先级',
                    medium: '中优先级',
                    low: '低优先级'
                };

                // 主图片
                const mainImage = wish.images && wish.images.length > 0 ? wish.images[0] : '';

                div.innerHTML = `
                    ${mainImage ? `
                        <div class="h-48 bg-gray-200 overflow-hidden">
                            <img src="${mainImage}" alt="${wish.title}" class="w-full h-full object-cover">
                        </div>
                    ` : `
                        <div class="h-48 bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center">
                            <i class="fa ${categoryIcons[wish.category] || 'fa-star'} text-4xl text-white"></i>
                        </div>
                    `}

                    <div class="p-6">
                        <div class="flex items-start justify-between mb-3">
                            <h3 class="text-lg font-bold text-gray-900 line-clamp-2">${wish.title}</h3>
                            <div class="flex space-x-1 ml-2">
                                <button onclick="wishlistManager.openModal('${wish.id}')" class="text-blue-600 hover:text-blue-700 p-1" title="编辑">
                                    <i class="fa fa-edit"></i>
                                </button>
                                <button onclick="wishlistManager.deleteWish('${wish.id}')" class="text-red-600 hover:text-red-700 p-1" title="删除">
                                    <i class="fa fa-trash"></i>
                                </button>
                            </div>
                        </div>

                        ${wish.description ? `<p class="text-gray-600 text-sm mb-4 line-clamp-3">${wish.description}</p>` : ''}

                        <div class="flex items-center justify-between mb-4">
                            <span class="px-2 py-1 rounded-full text-xs font-medium ${statusColors[wish.status]}">${statusTexts[wish.status]}</span>
                            <span class="text-xs ${priorityColors[wish.priority]}">${priorityTexts[wish.priority]}</span>
                        </div>

                        <div class="mb-4">
                            <div class="flex justify-between text-sm text-gray-600 mb-1">
                                <span>进度</span>
                                <span>${wish.progress || 0}%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: ${wish.progress || 0}%"></div>
                            </div>
                        </div>

                        ${wish.budget ? `
                            <div class="flex items-center text-sm text-gray-600 mb-2">
                                <i class="fa fa-money mr-2"></i>
                                <span>预算: ¥${wish.budget.toLocaleString()}</span>
                            </div>
                        ` : ''}

                        ${wish.targetDate ? `
                            <div class="flex items-center text-sm text-gray-600 mb-4">
                                <i class="fa fa-calendar mr-2"></i>
                                <span>目标: ${new Date(wish.targetDate).toLocaleDateString('zh-CN')}</span>
                            </div>
                        ` : ''}

                        <button onclick="wishlistManager.showWishDetail('${wish.id}')" class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 rounded-lg font-medium btn-effect">
                            查看详情
                        </button>
                    </div>
                `;

                return div;
            }

            showWishDetail(wishId) {
                const wish = this.wishes.find(w => w.id === wishId);
                if (!wish) return;

                const modal = document.getElementById('wish-detail-modal');
                const content = document.getElementById('wish-detail-content');

                const categoryTexts = {
                    travel: '旅行',
                    career: '事业',
                    financial: '财务',
                    health: '健康',
                    learning: '学习',
                    relationship: '人际关系',
                    hobby: '兴趣爱好',
                    other: '其他'
                };

                const statusTexts = {
                    planning: '计划中',
                    'in-progress': '进行中',
                    completed: '已完成'
                };

                const priorityTexts = {
                    high: '高优先级',
                    medium: '中优先级',
                    low: '低优先级'
                };

                content.innerHTML = `
                    <div class="relative">
                        <button onclick="wishlistManager.closeDetailModal()" class="absolute top-4 right-4 z-10 bg-white bg-opacity-80 hover:bg-opacity-100 rounded-full p-2 text-gray-600 hover:text-gray-800">
                            <i class="fa fa-times text-xl"></i>
                        </button>

                        ${wish.images && wish.images.length > 0 ? `
                            <div class="h-64 bg-gray-200 overflow-hidden">
                                <img src="${wish.images[0]}" alt="${wish.title}" class="w-full h-full object-cover">
                            </div>
                        ` : ''}

                        <div class="p-8">
                            <div class="mb-6">
                                <h2 class="text-3xl font-bold text-gray-900 mb-4">${wish.title}</h2>

                                <div class="flex flex-wrap gap-4 mb-4">
                                    <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">${categoryTexts[wish.category]}</span>
                                    <span class="px-3 py-1 bg-gray-100 text-gray-800 rounded-full text-sm">${statusTexts[wish.status]}</span>
                                    <span class="px-3 py-1 bg-purple-100 text-purple-800 rounded-full text-sm">${priorityTexts[wish.priority]}</span>
                                </div>

                                ${wish.description ? `<p class="text-gray-700 leading-relaxed mb-6">${wish.description}</p>` : ''}
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900 mb-4">基本信息</h3>
                                    <div class="space-y-3">
                                        <div class="flex justify-between">
                                            <span class="text-gray-600">当前进度</span>
                                            <span class="font-medium">${wish.progress || 0}%</span>
                                        </div>
                                        ${wish.budget ? `
                                            <div class="flex justify-between">
                                                <span class="text-gray-600">预估预算</span>
                                                <span class="font-medium">¥${wish.budget.toLocaleString()}</span>
                                            </div>
                                        ` : ''}
                                        ${wish.targetDate ? `
                                            <div class="flex justify-between">
                                                <span class="text-gray-600">目标日期</span>
                                                <span class="font-medium">${new Date(wish.targetDate).toLocaleDateString('zh-CN')}</span>
                                            </div>
                                        ` : ''}
                                        <div class="flex justify-between">
                                            <span class="text-gray-600">创建时间</span>
                                            <span class="font-medium">${new Date(wish.createdAt).toLocaleDateString('zh-CN')}</span>
                                        </div>
                                    </div>
                                </div>

                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900 mb-4">进度条</h3>
                                    <div class="w-full bg-gray-200 rounded-full h-4 mb-2">
                                        <div class="bg-blue-600 h-4 rounded-full transition-all duration-300" style="width: ${wish.progress || 0}%"></div>
                                    </div>
                                    <p class="text-sm text-gray-600">已完成 ${wish.progress || 0}%</p>
                                </div>
                            </div>

                            ${wish.steps && wish.steps.length > 0 ? `
                                <div class="mb-8">
                                    <h3 class="text-lg font-semibold text-gray-900 mb-4">实现步骤</h3>
                                    <ol class="space-y-2">
                                        ${wish.steps.map((step, index) => `
                                            <li class="flex items-start">
                                                <span class="flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium mr-3">${index + 1}</span>
                                                <span class="text-gray-700">${step}</span>
                                            </li>
                                        `).join('')}
                                    </ol>
                                </div>
                            ` : ''}

                            ${wish.images && wish.images.length > 1 ? `
                                <div class="mb-8">
                                    <h3 class="text-lg font-semibold text-gray-900 mb-4">相关图片</h3>
                                    <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
                                        ${wish.images.map(image => `
                                            <img src="${image}" alt="愿望图片" class="w-full h-32 object-cover rounded-lg cursor-pointer hover:opacity-80 transition-opacity">
                                        `).join('')}
                                    </div>
                                </div>
                            ` : ''}

                            <div class="flex justify-end space-x-4">
                                <button onclick="wishlistManager.openModal('${wish.id}')" class="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium">
                                    <i class="fa fa-edit mr-2"></i>编辑愿望
                                </button>
                                <button onclick="wishlistManager.toggleWishStatus('${wish.id}')" class="px-6 py-2 ${wish.status === 'completed' ? 'bg-yellow-600 hover:bg-yellow-700' : 'bg-green-600 hover:bg-green-700'} text-white rounded-lg font-medium">
                                    <i class="fa ${wish.status === 'completed' ? 'fa-undo' : 'fa-check'} mr-2"></i>${wish.status === 'completed' ? '标记未完成' : '标记完成'}
                                </button>
                            </div>
                        </div>
                    </div>
                `;

                modal.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            }

            toggleWishStatus(wishId) {
                const wish = this.wishes.find(w => w.id === wishId);
                if (!wish) return;

                if (wish.status === 'completed') {
                    wish.status = 'in-progress';
                    wish.progress = Math.min(wish.progress || 0, 99);
                } else {
                    wish.status = 'completed';
                    wish.progress = 100;
                }

                wish.updatedAt = new Date().toISOString();
                this.saveToStorage();
                this.renderWishes();
                this.updateStats();
                this.closeDetailModal();

                const message = wish.status === 'completed' ? '恭喜！愿望已实现！' : '愿望状态已更新';
                this.showNotification(message, 'success');
            }

            deleteWish(wishId) {
                if (!confirm('确定要删除这个愿望吗？此操作不可撤销。')) return;

                this.wishes = this.wishes.filter(w => w.id !== wishId);
                this.saveToStorage();
                this.renderWishes();
                this.updateStats();
                this.showNotification('愿望已删除', 'success');
            }

            applyFilters() {
                this.currentFilter.status = document.getElementById('status-filter').value;
                this.currentFilter.priority = document.getElementById('priority-filter').value;
                this.currentFilter.category = document.getElementById('category-filter').value;
                this.renderWishes();
            }

            clearFilters() {
                document.getElementById('status-filter').value = '';
                document.getElementById('priority-filter').value = '';
                document.getElementById('category-filter').value = '';
                this.currentFilter = { status: '', priority: '', category: '' };
                this.renderWishes();
            }

            updateStats() {
                const total = this.wishes.length;
                const completed = this.wishes.filter(w => w.status === 'completed').length;
                const inProgress = this.wishes.filter(w => w.status === 'in-progress').length;
                const completionRate = total > 0 ? Math.round((completed / total) * 100) : 0;

                document.getElementById('total-wishes').textContent = total;
                document.getElementById('completed-wishes').textContent = completed;
                document.getElementById('in-progress-wishes').textContent = inProgress;
                document.getElementById('completion-rate').textContent = completionRate + '%';
            }

            showNotification(message, type = 'info') {
                const notification = document.getElementById('notification');
                const icon = document.getElementById('notification-icon');
                const messageEl = document.getElementById('notification-message');

                const config = {
                    success: { icon: 'fa-check-circle', color: 'text-green-600', border: 'border-green-500' },
                    error: { icon: 'fa-exclamation-circle', color: 'text-red-600', border: 'border-red-500' },
                    info: { icon: 'fa-info-circle', color: 'text-blue-600', border: 'border-blue-500' }
                };

                const { icon: iconClass, color, border } = config[type] || config.info;

                icon.className = `fa ${iconClass} text-xl ${color}`;
                messageEl.textContent = message;
                notification.querySelector('.bg-white').className = `bg-white rounded-lg shadow-lg border-l-4 ${border} p-4`;

                notification.classList.remove('hidden');

                setTimeout(() => {
                    notification.classList.add('hidden');
                }, 3000);
            }
        }

        function hideNotification() {
            document.getElementById('notification').classList.add('hidden');
        }

        // 初始化应用
        const wishlistManager = new WishlistManager();
    </script>
</body>
</html>
