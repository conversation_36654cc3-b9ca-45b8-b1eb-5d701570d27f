<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设置 - 个人管理系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <style>
        .card-shadow {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }
        .btn-effect {
            transition: all 0.2s ease-in-out;
        }
        .btn-effect:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }
        /* 导航栏样式 */
        .nav-link {
            color: #6b7280;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            transition: all 0.2s ease;
            font-weight: 500;
        }
        .nav-link:hover {
            color: #3b82f6;
            background-color: #f3f4f6;
        }
        .nav-link.active {
            color: #3b82f6;
            background-color: #dbeafe;
            font-weight: 600;
        }
        .mobile-nav-link {
            color: #6b7280;
            text-decoration: none;
            transition: all 0.2s ease;
        }
        .mobile-nav-link:hover {
            color: #3b82f6;
            background-color: #f3f4f6;
        }
        .mobile-nav-link.active {
            color: #3b82f6;
            background-color: #dbeafe;
            font-weight: 600;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 统一导航栏 -->
    <nav class="bg-white shadow-md sticky top-0 left-0 right-0 z-50 transition-all duration-300">
        <div class="container mx-auto px-4">
            <div class="flex justify-between h-16">
                <!-- 左侧Logo/标题 -->
                <div class="flex items-center">
                    <a href="main-plan.html" class="flex items-center">
                        <i class="fa fa-apple text-blue-600 text-2xl mr-2"></i>
                        <span class="font-bold text-xl text-gray-900">生活NOTE</span>
                    </a>
                </div>

                <!-- 右侧导航链接 - 桌面端 -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="main-plan.html" class="nav-link" data-page="main-plan">
                        <i class="fa fa-home mr-2"></i>主计划
                    </a>
                    <a href="expense-tracker.html" class="nav-link" data-page="expense-tracker">
                        <i class="fa fa-money mr-2"></i>支出记录
                    </a>
                    <a href="wishlist.html" class="nav-link" data-page="wishlist">
                        <i class="fa fa-star mr-2"></i>愿望清单
                    </a>
                    <a href="settings.html" class="nav-link active" data-page="settings">
                        <i class="fa fa-cog mr-2"></i>设置
                    </a>
                </div>

                <!-- 移动端菜单按钮 -->
                <div class="md:hidden flex items-center">
                    <button id="mobile-menu-button" class="text-gray-600 hover:text-blue-600 focus:outline-none">
                        <i class="fa fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- 移动端导航菜单 -->
        <div id="mobile-menu" class="hidden md:hidden bg-white shadow-lg w-full">
            <div class="px-2 pt-2 pb-3 space-y-1">
                <a href="main-plan.html" class="mobile-nav-link block px-3 py-2 rounded-md" data-page="main-plan">
                    <i class="fa fa-home mr-2"></i>主计划
                </a>
                <a href="expense-tracker.html" class="mobile-nav-link block px-3 py-2 rounded-md" data-page="expense-tracker">
                    <i class="fa fa-money mr-2"></i>支出记录
                </a>
                <a href="wishlist.html" class="mobile-nav-link block px-3 py-2 rounded-md" data-page="wishlist">
                    <i class="fa fa-star mr-2"></i>愿望清单
                </a>
                <a href="settings.html" class="mobile-nav-link active block px-3 py-2 rounded-md" data-page="settings">
                    <i class="fa fa-cog mr-2"></i>设置
                </a>
            </div>
        </div>
    </nav>

    <!-- 主内容 -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 页面头部 -->
        <div class="mb-8">
            <h2 class="text-3xl font-bold text-gray-900 mb-2">系统设置</h2>
            <p class="text-gray-600">管理您的数据、备份和系统配置</p>
        </div>

        <!-- 设置卡片网格 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- 数据管理 -->
            <div class="bg-white rounded-xl p-6 card-shadow">
                <div class="flex items-center mb-6">
                    <div class="p-3 rounded-full bg-blue-100 text-blue-600 mr-4">
                        <i class="fa fa-database text-xl"></i>
                    </div>
                    <div>
                        <h3 class="text-xl font-bold text-gray-900">数据管理</h3>
                        <p class="text-gray-600">导入、导出和备份您的数据</p>
                    </div>
                </div>

                <div class="space-y-4">
                    <!-- 数据导入 -->
                    <div class="border border-gray-200 rounded-lg p-4">
                        <h4 class="font-semibold text-gray-900 mb-2">
                            <i class="fa fa-upload mr-2 text-green-600"></i>数据导入
                        </h4>
                        <p class="text-sm text-gray-600 mb-3">从备份文件恢复您的数据</p>
                        <div class="flex items-center space-x-3">
                            <input type="file" id="import-file" accept=".json" class="hidden">
                            <button onclick="document.getElementById('import-file').click()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg btn-effect">
                                <i class="fa fa-folder-open mr-2"></i>选择文件
                            </button>
                            <button onclick="importFromProject()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg btn-effect">
                                <i class="fa fa-download mr-2"></i>导入项目备份
                            </button>
                        </div>
                    </div>

                    <!-- 数据导出 -->
                    <div class="border border-gray-200 rounded-lg p-4">
                        <h4 class="font-semibold text-gray-900 mb-2">
                            <i class="fa fa-download mr-2 text-blue-600"></i>数据导出
                        </h4>
                        <p class="text-sm text-gray-600 mb-3">备份您的所有数据</p>
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-2">
                            <button onclick="exportAllData()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg btn-effect">
                                <i class="fa fa-download mr-2"></i>导出全部
                            </button>
                            <button onclick="exportByType()" class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg btn-effect">
                                <i class="fa fa-filter mr-2"></i>分类导出
                            </button>
                        </div>
                    </div>

                    <!-- 数据清理 -->
                    <div class="border border-gray-200 rounded-lg p-4">
                        <h4 class="font-semibold text-gray-900 mb-2">
                            <i class="fa fa-trash mr-2 text-red-600"></i>数据清理
                        </h4>
                        <p class="text-sm text-gray-600 mb-3">清理旧数据释放存储空间</p>
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-2">
                            <button onclick="clearOldHistory()" class="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg btn-effect">
                                <i class="fa fa-calendar mr-2"></i>清理历史
                            </button>
                            <button onclick="clearAllData()" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg btn-effect">
                                <i class="fa fa-exclamation-triangle mr-2"></i>清空全部
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 存储分析 -->
            <div class="bg-white rounded-xl p-6 card-shadow">
                <div class="flex items-center mb-6">
                    <div class="p-3 rounded-full bg-green-100 text-green-600 mr-4">
                        <i class="fa fa-pie-chart text-xl"></i>
                    </div>
                    <div>
                        <h3 class="text-xl font-bold text-gray-900">存储分析</h3>
                        <p class="text-gray-600">查看数据使用情况和统计</p>
                    </div>
                </div>

                <!-- 存储概览 -->
                <div id="storage-overview" class="space-y-4 mb-6">
                    <!-- 动态生成 -->
                </div>

                <button onclick="refreshStorageAnalysis()" class="w-full bg-green-600 hover:bg-green-700 text-white py-2 rounded-lg font-medium btn-effect">
                    <i class="fa fa-refresh mr-2"></i>刷新分析
                </button>
            </div>

            <!-- 系统信息 -->
            <div class="bg-white rounded-xl p-6 card-shadow">
                <div class="flex items-center mb-6">
                    <div class="p-3 rounded-full bg-purple-100 text-purple-600 mr-4">
                        <i class="fa fa-info-circle text-xl"></i>
                    </div>
                    <div>
                        <h3 class="text-xl font-bold text-gray-900">系统信息</h3>
                        <p class="text-gray-600">查看系统状态和版本信息</p>
                    </div>
                </div>

                <div id="system-info" class="space-y-3">
                    <!-- 动态生成 -->
                </div>
            </div>

            <!-- 功能测试 -->
            <div class="bg-white rounded-xl p-6 card-shadow">
                <div class="flex items-center mb-6">
                    <div class="p-3 rounded-full bg-orange-100 text-orange-600 mr-4">
                        <i class="fa fa-flask text-xl"></i>
                    </div>
                    <div>
                        <h3 class="text-xl font-bold text-gray-900">功能测试</h3>
                        <p class="text-gray-600">测试各项功能是否正常工作</p>
                    </div>
                </div>

                <div class="space-y-3">
                    <button onclick="testLocalStorage()" class="w-full bg-orange-600 hover:bg-orange-700 text-white py-2 rounded-lg btn-effect">
                        <i class="fa fa-database mr-2"></i>测试本地存储
                    </button>
                    <button onclick="testDataIntegrity()" class="w-full bg-orange-600 hover:bg-orange-700 text-white py-2 rounded-lg btn-effect">
                        <i class="fa fa-check-circle mr-2"></i>检查数据完整性
                    </button>
                    <button onclick="generateTestData()" class="w-full bg-orange-600 hover:bg-orange-700 text-white py-2 rounded-lg btn-effect">
                        <i class="fa fa-plus mr-2"></i>生成测试数据
                    </button>
                </div>
            </div>
        </div>

        <!-- 操作结果显示 -->
        <div id="operation-result" class="mt-8 hidden">
            <div class="bg-white rounded-xl p-6 card-shadow">
                <h3 class="text-lg font-bold text-gray-900 mb-4">操作结果</h3>
                <div id="result-content" class="text-sm"></div>
            </div>
        </div>
    </main>

    <!-- 通知组件 -->
    <div id="notification" class="hidden fixed top-4 right-4 z-50 max-w-sm">
        <div class="bg-white rounded-lg shadow-lg border-l-4 p-4">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i id="notification-icon" class="fa text-xl"></i>
                </div>
                <div class="ml-3">
                    <p id="notification-message" class="text-sm font-medium text-gray-900"></p>
                </div>
                <div class="ml-auto pl-3">
                    <button onclick="hideNotification()" class="text-gray-400 hover:text-gray-600">
                        <i class="fa fa-times"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 确认对话框 -->
    <div id="confirm-modal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
        <div class="bg-white rounded-xl max-w-md w-full p-6">
            <div class="text-center">
                <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                    <i class="fa fa-exclamation-triangle text-red-600 text-xl"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">确认操作</h3>
                <p id="confirm-message" class="text-sm text-gray-500 mb-6"></p>
                <div class="flex space-x-4">
                    <button id="confirm-cancel" class="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-800 py-2 px-4 rounded-lg">
                        取消
                    </button>
                    <button id="confirm-ok" class="flex-1 bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-lg">
                        确认
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 设置管理类
        class SettingsManager {
            constructor() {
                this.init();
            }

            init() {
                this.setupEventListeners();
                this.refreshStorageAnalysis();
                this.updateSystemInfo();
            }

            setupEventListeners() {
                // 移动端菜单
                document.getElementById('mobile-menu-button').addEventListener('click', () => {
                    const menu = document.getElementById('mobile-menu');
                    menu.classList.toggle('hidden');
                });

                // 文件导入
                document.getElementById('import-file').addEventListener('change', (e) => {
                    this.handleFileImport(e);
                });

                // 确认对话框
                document.getElementById('confirm-cancel').addEventListener('click', () => {
                    this.hideConfirmModal();
                });
            }

            // 数据导入功能
            handleFileImport(event) {
                const file = event.target.files[0];
                if (!file) return;

                const reader = new FileReader();
                reader.onload = (e) => {
                    try {
                        const data = JSON.parse(e.target.result);
                        this.importData(data);
                    } catch (error) {
                        this.showNotification('文件格式错误，请选择有效的JSON文件', 'error');
                    }
                };
                reader.readAsText(file);
            }

            importData(data) {
                try {
                    let importCount = 0;
                    let resultMessage = '数据导入结果：<br><br>';

                    // 导入主计划数据
                    if (data.dailyPlanner) {
                        localStorage.setItem('dailyPlanner', JSON.stringify(data.dailyPlanner));
                        importCount++;
                        resultMessage += '✅ 主计划数据已导入<br>';
                    }

                    // 导入支出记录数据
                    if (data.expenses) {
                        localStorage.setItem('expenses', JSON.stringify(data.expenses));
                        importCount++;
                        resultMessage += '✅ 支出记录数据已导入<br>';
                    }

                    // 导入愿望清单数据
                    if (data.wishlist) {
                        localStorage.setItem('wishlist', JSON.stringify(data.wishlist));
                        importCount++;
                        resultMessage += '✅ 愿望清单数据已导入<br>';
                    }

                    // 导入其他数据
                    Object.keys(data).forEach(key => {
                        if (!['dailyPlanner', 'expenses', 'wishlist'].includes(key)) {
                            localStorage.setItem(key, JSON.stringify(data[key]));
                            importCount++;
                            resultMessage += `✅ ${key} 数据已导入<br>`;
                        }
                    });

                    if (importCount > 0) {
                        resultMessage += `<br>共导入 ${importCount} 项数据`;
                        this.showResult(resultMessage);
                        this.showNotification('数据导入成功！', 'success');
                        this.refreshStorageAnalysis();
                    } else {
                        this.showNotification('未找到可导入的数据', 'error');
                    }

                } catch (error) {
                    console.error('导入数据时出错:', error);
                    this.showNotification('导入数据时出错', 'error');
                }
            }

            // 从项目中导入备份文件
            async importFromProject() {
                try {
                    const response = await fetch('./本地存储备份_2025-08-05.json');
                    if (response.ok) {
                        const data = await response.json();
                        this.importData(data);
                    } else {
                        this.showNotification('未找到项目备份文件', 'error');
                    }
                } catch (error) {
                    console.error('读取项目备份文件失败:', error);
                    this.showNotification('读取项目备份文件失败', 'error');
                }
            }

            // 导出所有数据
            exportAllData() {
                const allData = {};

                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    try {
                        allData[key] = JSON.parse(localStorage.getItem(key));
                    } catch (e) {
                        allData[key] = localStorage.getItem(key);
                    }
                }

                this.downloadData(allData, '完整备份');
                this.showNotification('数据导出成功', 'success');
            }

            // 分类导出数据
            exportByType() {
                const exports = {
                    '主计划数据': localStorage.getItem('dailyPlanner'),
                    '支出记录数据': localStorage.getItem('expenses'),
                    '愿望清单数据': localStorage.getItem('wishlist')
                };

                Object.keys(exports).forEach(name => {
                    if (exports[name]) {
                        try {
                            const data = JSON.parse(exports[name]);
                            this.downloadData(data, name);
                        } catch (e) {
                            console.error(`导出${name}失败:`, e);
                        }
                    }
                });

                this.showNotification('分类数据导出完成', 'success');
            }

            downloadData(data, filename) {
                const dataStr = JSON.stringify(data, null, 2);
                const dataBlob = new Blob([dataStr], { type: 'application/json' });
                const link = document.createElement('a');
                link.href = URL.createObjectURL(dataBlob);
                link.download = `${filename}_${new Date().toISOString().split('T')[0]}.json`;
                link.click();
            }

            // 清理历史数据
            clearOldHistory() {
                this.showConfirmModal(
                    '确定要清理30天前的历史数据吗？此操作不可撤销。',
                    () => {
                        try {
                            const plannerData = JSON.parse(localStorage.getItem('dailyPlanner') || '{}');
                            const thirtyDaysAgo = new Date();
                            thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

                            let cleaned = 0;
                            if (plannerData.history) {
                                Object.keys(plannerData.history).forEach(dateKey => {
                                    const date = new Date(dateKey);
                                    if (date < thirtyDaysAgo) {
                                        delete plannerData.history[dateKey];
                                        cleaned++;
                                    }
                                });
                            }

                            localStorage.setItem('dailyPlanner', JSON.stringify(plannerData));
                            this.showNotification(`已清理 ${cleaned} 天的历史数据`, 'success');
                            this.refreshStorageAnalysis();
                        } catch (e) {
                            this.showNotification('清理历史数据失败', 'error');
                        }
                    }
                );
            }

            // 清空所有数据
            clearAllData() {
                this.showConfirmModal(
                    '⚠️ 警告：此操作将清空所有数据，包括主计划、支出记录、愿望清单等。此操作不可撤销！',
                    () => {
                        localStorage.clear();
                        this.showNotification('所有数据已清空', 'success');
                        this.refreshStorageAnalysis();
                        this.updateSystemInfo();
                    }
                );
            }

            // 刷新存储分析
            refreshStorageAnalysis() {
                const analysis = this.analyzeStorage();
                this.displayStorageOverview(analysis);
            }

            analyzeStorage() {
                const analysis = {
                    total: 0,
                    items: [],
                    breakdown: {}
                };

                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    const value = localStorage.getItem(key);
                    const size = new Blob([value]).size;

                    analysis.items.push({
                        key: key,
                        size: size,
                        value: value
                    });

                    analysis.total += size;
                }

                analysis.items.sort((a, b) => b.size - a.size);
                return analysis;
            }

            displayStorageOverview(analysis) {
                const container = document.getElementById('storage-overview');
                const totalMB = analysis.total / (1024 * 1024);
                const usagePercent = (totalMB / 5) * 100;

                container.innerHTML = `
                    <div class="flex justify-between items-center p-3 bg-blue-50 rounded">
                        <span class="font-medium">总使用量</span>
                        <span class="text-blue-600 font-bold">${this.formatBytes(analysis.total)}</span>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-gray-50 rounded">
                        <span class="font-medium">存储项数量</span>
                        <span class="text-gray-600">${analysis.items.length} 项</span>
                    </div>
                    <div class="p-3 bg-yellow-50 rounded">
                        <div class="flex justify-between items-center mb-2">
                            <span class="font-medium">使用率</span>
                            <span class="text-yellow-600">${usagePercent.toFixed(2)}%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-yellow-500 h-2 rounded-full" style="width: ${Math.min(usagePercent, 100)}%"></div>
                        </div>
                        <div class="text-xs text-gray-500 mt-1">基于5MB限制估算</div>
                    </div>
                    ${analysis.items.length > 0 ? `
                        <div class="space-y-2">
                            <h4 class="font-medium text-gray-900">存储详情</h4>
                            ${analysis.items.slice(0, 5).map(item => {
                                const percent = (item.size / analysis.total * 100).toFixed(1);
                                return `
                                    <div class="flex justify-between items-center text-sm">
                                        <span class="text-gray-600">${item.key}</span>
                                        <span class="text-gray-500">${this.formatBytes(item.size)} (${percent}%)</span>
                                    </div>
                                `;
                            }).join('')}
                        </div>
                    ` : ''}
                `;
            }

            formatBytes(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

            // 更新系统信息
            updateSystemInfo() {
                const container = document.getElementById('system-info');
                const plannerData = localStorage.getItem('dailyPlanner');
                const expenseData = localStorage.getItem('expenses');
                const wishlistData = localStorage.getItem('wishlist');

                let plannerStats = { tasks: 0, history: 0 };
                let expenseStats = { total: 0 };
                let wishlistStats = { total: 0 };

                try {
                    if (plannerData) {
                        const data = JSON.parse(plannerData);
                        plannerStats.tasks = Object.keys(data.tasks || {}).reduce((sum, date) => sum + (data.tasks[date]?.length || 0), 0);
                        plannerStats.history = Object.keys(data.history || {}).length;
                    }
                    if (expenseData) {
                        expenseStats.total = JSON.parse(expenseData).length;
                    }
                    if (wishlistData) {
                        wishlistStats.total = JSON.parse(wishlistData).length;
                    }
                } catch (e) {
                    console.error('解析数据时出错:', e);
                }

                container.innerHTML = `
                    <div class="flex justify-between">
                        <span class="text-gray-600">浏览器</span>
                        <span class="font-medium">${navigator.userAgent.split(' ')[0]}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">localStorage支持</span>
                        <span class="font-medium text-green-600">✓ 支持</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">主计划任务</span>
                        <span class="font-medium">${plannerStats.tasks} 个</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">历史记录</span>
                        <span class="font-medium">${plannerStats.history} 天</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">支出记录</span>
                        <span class="font-medium">${expenseStats.total} 条</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">愿望清单</span>
                        <span class="font-medium">${wishlistStats.total} 个</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">最后更新</span>
                        <span class="font-medium">${new Date().toLocaleString('zh-CN')}</span>
                    </div>
                `;
            }

            // 测试本地存储
            testLocalStorage() {
                try {
                    const testKey = 'test_' + Date.now();
                    const testValue = 'localStorage测试数据';

                    localStorage.setItem(testKey, testValue);
                    const retrieved = localStorage.getItem(testKey);
                    localStorage.removeItem(testKey);

                    if (retrieved === testValue) {
                        this.showNotification('本地存储功能正常', 'success');
                    } else {
                        this.showNotification('本地存储读取异常', 'error');
                    }
                } catch (e) {
                    this.showNotification('本地存储功能异常: ' + e.message, 'error');
                }
            }

            // 检查数据完整性
            testDataIntegrity() {
                let result = '数据完整性检查结果：<br><br>';
                let hasIssues = false;

                // 检查主计划数据
                try {
                    const plannerData = localStorage.getItem('dailyPlanner');
                    if (plannerData) {
                        const data = JSON.parse(plannerData);
                        result += '✅ 主计划数据格式正确<br>';
                        result += `   - 任务数据: ${Object.keys(data.tasks || {}).length} 个日期<br>`;
                        result += `   - 历史记录: ${Object.keys(data.history || {}).length} 天<br>`;
                    } else {
                        result += '⚠️ 主计划数据不存在<br>';
                        hasIssues = true;
                    }
                } catch (e) {
                    result += '❌ 主计划数据格式错误<br>';
                    hasIssues = true;
                }

                // 检查支出记录数据
                try {
                    const expenseData = localStorage.getItem('expenses');
                    if (expenseData) {
                        const data = JSON.parse(expenseData);
                        result += '✅ 支出记录数据格式正确<br>';
                        result += `   - 记录数量: ${data.length} 条<br>`;
                    } else {
                        result += '⚠️ 支出记录数据不存在<br>';
                        hasIssues = true;
                    }
                } catch (e) {
                    result += '❌ 支出记录数据格式错误<br>';
                    hasIssues = true;
                }

                // 检查愿望清单数据
                try {
                    const wishlistData = localStorage.getItem('wishlist');
                    if (wishlistData) {
                        const data = JSON.parse(wishlistData);
                        result += '✅ 愿望清单数据格式正确<br>';
                        result += `   - 愿望数量: ${data.length} 个<br>`;
                    } else {
                        result += '⚠️ 愿望清单数据不存在<br>';
                        hasIssues = true;
                    }
                } catch (e) {
                    result += '❌ 愿望清单数据格式错误<br>';
                    hasIssues = true;
                }

                result += '<br>' + (hasIssues ? '发现一些问题，建议检查数据' : '所有数据完整性良好');
                this.showResult(result);
            }

            // 生成测试数据
            generateTestData() {
                this.showConfirmModal(
                    '确定要生成测试数据吗？这将添加一些示例数据到您的系统中。',
                    () => {
                        this.createTestData();
                    }
                );
            }

            createTestData() {
                try {
                    // 生成主计划测试数据
                    const plannerData = JSON.parse(localStorage.getItem('dailyPlanner') || '{"tasks":{},"coreTasks":{},"improvements":[],"reflections":{},"ratings":{},"history":{}}');

                    const today = new Date();
                    const todayKey = today.toISOString().split('T')[0];

                    if (!plannerData.tasks[todayKey]) {
                        plannerData.tasks[todayKey] = [];
                    }

                    plannerData.tasks[todayKey].push(
                        { id: 'test_' + Date.now() + '_1', text: '测试任务1', completed: false, category: 'life' },
                        { id: 'test_' + Date.now() + '_2', text: '测试任务2', completed: true, category: 'work' }
                    );

                    localStorage.setItem('dailyPlanner', JSON.stringify(plannerData));

                    // 生成支出记录测试数据
                    const expenseData = JSON.parse(localStorage.getItem('expenses') || '[]');
                    expenseData.push(
                        { id: 'test_' + Date.now() + '_1', date: todayKey, category: '餐饮', amount: 25.50, note: '测试午餐' },
                        { id: 'test_' + Date.now() + '_2', date: todayKey, category: '交通', amount: 12.00, note: '测试地铁' }
                    );
                    localStorage.setItem('expenses', JSON.stringify(expenseData));

                    // 生成愿望清单测试数据
                    const wishlistData = JSON.parse(localStorage.getItem('wishlist') || '[]');
                    wishlistData.push({
                        id: 'test_' + Date.now(),
                        title: '测试愿望',
                        description: '这是一个测试愿望',
                        category: 'travel',
                        priority: 'medium',
                        status: 'planning',
                        progress: 20,
                        createdAt: new Date().toISOString(),
                        images: [],
                        steps: ['第一步', '第二步']
                    });
                    localStorage.setItem('wishlist', JSON.stringify(wishlistData));

                    this.showNotification('测试数据生成成功', 'success');
                    this.refreshStorageAnalysis();
                    this.updateSystemInfo();
                } catch (e) {
                    this.showNotification('生成测试数据失败', 'error');
                }
            }

            // 显示确认对话框
            showConfirmModal(message, onConfirm) {
                document.getElementById('confirm-message').textContent = message;
                document.getElementById('confirm-modal').classList.remove('hidden');

                const confirmBtn = document.getElementById('confirm-ok');
                const newConfirmBtn = confirmBtn.cloneNode(true);
                confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);

                newConfirmBtn.addEventListener('click', () => {
                    this.hideConfirmModal();
                    onConfirm();
                });
            }

            hideConfirmModal() {
                document.getElementById('confirm-modal').classList.add('hidden');
            }

            // 显示操作结果
            showResult(content) {
                document.getElementById('result-content').innerHTML = content;
                document.getElementById('operation-result').classList.remove('hidden');

                // 滚动到结果区域
                document.getElementById('operation-result').scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }

            // 显示通知
            showNotification(message, type = 'info') {
                const notification = document.getElementById('notification');
                const icon = document.getElementById('notification-icon');
                const messageEl = document.getElementById('notification-message');

                const config = {
                    success: { icon: 'fa-check-circle', color: 'text-green-600', border: 'border-green-500' },
                    error: { icon: 'fa-exclamation-circle', color: 'text-red-600', border: 'border-red-500' },
                    info: { icon: 'fa-info-circle', color: 'text-blue-600', border: 'border-blue-500' }
                };

                const { icon: iconClass, color, border } = config[type] || config.info;

                icon.className = `fa ${iconClass} text-xl ${color}`;
                messageEl.textContent = message;
                notification.querySelector('.bg-white').className = `bg-white rounded-lg shadow-lg border-l-4 ${border} p-4`;

                notification.classList.remove('hidden');

                setTimeout(() => {
                    notification.classList.add('hidden');
                }, 3000);
            }
        }

        // 全局函数
        function hideNotification() {
            document.getElementById('notification').classList.add('hidden');
        }

        function importFromProject() {
            settingsManager.importFromProject();
        }

        function exportAllData() {
            settingsManager.exportAllData();
        }

        function exportByType() {
            settingsManager.exportByType();
        }

        function clearOldHistory() {
            settingsManager.clearOldHistory();
        }

        function clearAllData() {
            settingsManager.clearAllData();
        }

        function refreshStorageAnalysis() {
            settingsManager.refreshStorageAnalysis();
        }

        function testLocalStorage() {
            settingsManager.testLocalStorage();
        }

        function testDataIntegrity() {
            settingsManager.testDataIntegrity();
        }

        function generateTestData() {
            settingsManager.generateTestData();
        }

        // 初始化应用
        const settingsManager = new SettingsManager();
    </script>
</body>
</html>