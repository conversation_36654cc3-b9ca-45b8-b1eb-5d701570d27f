<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>记忆强化 - 基于遗忘曲线的间隔复习</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#4F46E5',
                        secondary: '#10B981',
                        accent: '#F59E0B',
                        dark: '#1F2937',
                        light: '#F9FAFB'
                    },
                    fontFamily: {
                        sans: ['Inter', 'system-ui', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            .content-auto {
                content-visibility: auto;
            }
            .card-shadow {
                box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            }
            .transition-custom {
                transition: all 0.3s ease;
            }
        }
    </style>
</head>
<body class="bg-gray-50 font-sans text-dark">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-md fixed w-full z-10 transition-custom">
        <div class="container mx-auto px-4 py-3 flex justify-between items-center">
            <div class="flex items-center space-x-2">
                <i class="fa fa-brain text-primary text-2xl"></i>
                <h1 class="text-xl font-bold text-primary">记忆强化</h1>
            </div>
            <div class="hidden md:flex items-center space-x-6">
                <a href="#dashboard" class="font-medium hover:text-primary transition-custom">学习 dashboard</a>
                <a href="#add-content" class="font-medium hover:text-primary transition-custom">添加学习内容</a>
                <a href="#review" class="font-medium hover:text-primary transition-custom">今日复习</a>
                <a href="#about" class="font-medium hover:text-primary transition-custom">关于遗忘曲线</a>
            </div>
            <button class="md:hidden text-dark text-xl" id="menu-toggle">
                <i class="fa fa-bars"></i>
            </button>
        </div>
        <!-- 移动端菜单 -->
        <div id="mobile-menu" class="hidden md:hidden bg-white border-t">
            <div class="container mx-auto px-4 py-2 flex flex-col space-y-3">
                <a href="#dashboard" class="py-2 font-medium hover:text-primary transition-custom">学习 dashboard</a>
                <a href="#add-content" class="py-2 font-medium hover:text-primary transition-custom">添加学习内容</a>
                <a href="#review" class="py-2 font-medium hover:text-primary transition-custom">今日复习</a>
                <a href="#about" class="py-2 font-medium hover:text-primary transition-custom">关于遗忘曲线</a>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="container mx-auto px-4 pt-24 pb-16">
        <!-- 欢迎信息 -->
        <section id="dashboard" class="mb-12">
            <div class="bg-gradient-to-r from-primary to-indigo-600 rounded-xl p-6 md:p-8 text-white shadow-lg">
                <h2 class="text-2xl md:text-3xl font-bold mb-4">欢迎回来！</h2>
                <p class="mb-6 text-indigo-100">今天是 <span id="current-date" class="font-medium"></span>，让我们高效学习，科学复习</p>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="bg-white/20 backdrop-blur-sm rounded-lg p-4">
                        <div class="flex items-center justify-between mb-2">
                            <h3 class="font-medium">今日需要复习</h3>
                            <i class="fa fa-calendar-check-o"></i>
                        </div>
                        <p class="text-2xl font-bold" id="today-review-count">0</p>
                    </div>
                    <div class="bg-white/20 backdrop-blur-sm rounded-lg p-4">
                        <div class="flex items-center justify-between mb-2">
                            <h3 class="font-medium">总学习内容</h3>
                            <i class="fa fa-book"></i>
                        </div>
                        <p class="text-2xl font-bold" id="total-content-count">0</p>
                    </div>
                    <div class="bg-white/20 backdrop-blur-sm rounded-lg p-4">
                        <div class="flex items-center justify-between mb-2">
                            <h3 class="font-medium">长期循环复习</h3>
                            <i class="fa fa-refresh"></i>
                        </div>
                        <p class="text-2xl font-bold" id="cycling-content-count">0</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 添加学习内容 -->
        <section id="add-content" class="mb-12">
            <div class="bg-white rounded-xl shadow-md p-6 md:p-8">
                <h2 class="text-2xl font-bold mb-6 flex items-center">
                    <i class="fa fa-plus-circle text-primary mr-2"></i>添加新的学习内容
                </h2>
                <form id="content-form" class="space-y-4">
                    <div>
                        <label for="content-title" class="block text-sm font-medium text-gray-700 mb-1">标题</label>
                        <input type="text" id="content-title" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-custom" placeholder="例如：微积分基本定理" required>
                    </div>
                    <div>
                        <label for="content-category" class="block text-sm font-medium text-gray-700 mb-1">类别</label>
                        <select id="content-category" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-custom">
                            <option value="math">数学</option>
                            <option value="computer">计算机</option>
                            <option value="english">英语</option>
                            <option value="politics">政治</option>
                            <option value="other">其他</option>
                        </select>
                    </div>
                    <div>
                        <label for="content-description" class="block text-sm font-medium text-gray-700 mb-1">详细描述</label>
                        <textarea id="content-description" rows="4" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-custom" placeholder="记录关键知识点、公式、理解要点等..."></textarea>
                    </div>
                    <div>
                        <label for="content-connections" class="block text-sm font-medium text-gray-700 mb-1">主动关联的旧知识</label>
                        <textarea id="content-connections" rows="2" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-custom" placeholder="记录与已学知识的关联，帮助构建知识网络..."></textarea>
                    </div>
                    <div>
                        <label for="content-difficulty" class="block text-sm font-medium text-gray-700 mb-1">难度</label>
                        <div class="flex items-center space-x-4">
                            <label class="inline-flex items-center">
                                <input type="radio" name="difficulty" value="easy" class="form-radio text-primary" checked>
                                <span class="ml-2">简单</span>
                            </label>
                            <label class="inline-flex items-center">
                                <input type="radio" name="difficulty" value="medium" class="form-radio text-primary">
                                <span class="ml-2">中等</span>
                            </label>
                            <label class="inline-flex items-center">
                                <input type="radio" name="difficulty" value="hard" class="form-radio text-primary">
                                <span class="ml-2">困难</span>
                            </label>
                        </div>
                    </div>
                    <button type="submit" class="bg-primary hover:bg-indigo-700 text-white px-6 py-3 rounded-lg font-medium transition-custom flex items-center">
                        <i class="fa fa-save mr-2"></i>保存学习内容
                    </button>
                </form>
            </div>
        </section>

        <!-- 今日复习 -->
        <section id="review" class="mb-12">
            <div class="bg-white rounded-xl shadow-md p-6 md:p-8">
                <h2 class="text-2xl font-bold mb-6 flex items-center">
                    <i class="fa fa-refresh text-accent mr-2"></i>今日复习内容
                </h2>
                <div id="today-reviews" class="space-y-4">
                    <!-- 复习内容将通过JavaScript动态生成 -->
                    <div class="text-center py-8 text-gray-500" id="no-review-message">
                        <i class="fa fa-check-circle text-4xl mb-4 text-gray-300"></i>
                        <p>今天没有需要复习的内容，真棒！</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 所有学习内容 -->
        <section id="all-content" class="mb-12">
            <div class="bg-white rounded-xl shadow-md p-6 md:p-8">
                <h2 class="text-2xl font-bold mb-6 flex items-center">
                    <i class="fa fa-list text-dark mr-2"></i>所有学习内容
                </h2>
                <div class="flex justify-between items-center mb-4">
                    <div class="relative">
                        <input type="text" id="search-content" placeholder="搜索学习内容..." class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-custom">
                        <i class="fa fa-search absolute left-3 top-3 text-gray-400"></i>
                    </div>
                    <select id="filter-category" class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-custom">
                        <option value="all">所有类别</option>
                        <option value="math">数学</option>
                        <option value="computer">计算机</option>
                        <option value="english">英语</option>
                        <option value="politics">政治</option>
                        <option value="other">其他</option>
                    </select>
                </div>
                <div id="content-list" class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- 学习内容将通过JavaScript动态生成 -->
                    <div class="text-center py-8 text-gray-500 col-span-full" id="no-content-message">
                        <i class="fa fa-book-open text-4xl mb-4 text-gray-300"></i>
                        <p>还没有添加学习内容，点击"添加学习内容"开始记录吧！</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 关于遗忘曲线 -->
        <section id="about" class="mb-12">
            <div class="bg-white rounded-xl shadow-md p-6 md:p-8">
                <h2 class="text-2xl font-bold mb-6 flex items-center">
                    <i class="fa fa-line-chart text-secondary mr-2"></i>关于遗忘曲线与间隔复习
                </h2>
                <div class="space-y-4">
                    <p>艾宾浩斯遗忘曲线表明，人类记忆的遗忘是有规律的，新学习的知识在短时间内会快速遗忘，然后遗忘速度逐渐减慢。我们的系统结合了初始强化与长期循环复习，确保知识长期留存。</p>
                    
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h3 class="font-bold text-lg mb-2">完整复习周期（含循环）：</h3>
                        <ul class="list-disc pl-5 space-y-1">
                            <li><span class="font-medium">初始强化阶段：</span>1天 → 3天 → 7天 → 14天</li>
                            <li><span class="font-medium">长期循环阶段（根据难度）：</span></li>
                            <li class="pl-6">- 简单内容：28天 → 60天循环 → 60天循环...</li>
                            <li class="pl-6">- 中等内容：21天 → 45天循环 → 45天循环...</li>
                            <li class="pl-6">- 困难内容：14天 → 30天循环 → 30天循环...</li>
                        </ul>
                    </div>
                    
                    <p>14天后进入循环复习阶段，系统会根据内容难度自动调整循环间隔。每次复习时，你可以根据记忆情况动态调整下一次间隔，形成个性化的长期记忆方案。</p>
                    
                    <div class="mt-6">
                        <canvas id="forgetting-curve" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- 复习模态框 -->
    <div id="review-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-xl max-w-lg w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div class="p-6 border-b">
                <div class="flex justify-between items-center">
                    <h3 class="text-xl font-bold" id="modal-title">复习内容</h3>
                    <button id="close-modal" class="text-gray-500 hover:text-gray-700">
                        <i class="fa fa-times text-xl"></i>
                    </button>
                </div>
                <p class="text-sm text-gray-500 mt-1" id="modal-category"></p>
            </div>
            <div class="p-6">
                <div class="mb-6">
                    <h4 class="font-medium text-gray-700 mb-2">学习内容：</h4>
                    <p id="modal-description" class="bg-gray-50 p-4 rounded-lg"></p>
                </div>
                <div class="mb-6">
                    <h4 class="font-medium text-gray-700 mb-2">关联的旧知识：</h4>
                    <p id="modal-connections" class="bg-blue-50 p-4 rounded-lg text-blue-800 italic"></p>
                </div>
                <div class="mb-6">
                    <h4 class="font-medium text-gray-700 mb-2">自我测试：</h4>
                    <p class="text-gray-600 mb-4">回忆并复述上述内容，然后评估你的记忆程度：</p>
                    <div class="flex flex-col space-y-3">
                        <label class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                            <input type="radio" name="memory-level" value="forgot" class="form-radio text-red-500" checked>
                            <div class="ml-3">
                                <span class="font-medium">几乎忘了</span>
                                <p class="text-sm text-gray-500">需要重新学习大部分内容</p>
                            </div>
                        </label>
                        <label class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                            <input type="radio" name="memory-level" value="partial" class="form-radio text-yellow-500">
                            <div class="ml-3">
                                <span class="font-medium">部分记得</span>
                                <p class="text-sm text-gray-500">记得一部分，但不够清晰</p>
                            </div>
                        </label>
                        <label class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                            <input type="radio" name="memory-level" value="remember" class="form-radio text-green-500">
                            <div class="ml-3">
                                <span class="font-medium">大部分记得</span>
                                <p class="text-sm text-gray-500">记得大部分内容，细节可能模糊</p>
                            </div>
                        </label>
                        <label class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                            <input type="radio" name="memory-level" value="mastered" class="form-radio text-primary">
                            <div class="ml-3">
                                <span class="font-medium">完全掌握</span>
                                <p class="text-sm text-gray-500">清晰记得所有内容</p>
                            </div>
                        </label>
                    </div>
                </div>
                
                <!-- 循环调整部分（仅在循环阶段显示） -->
                <div id="cycle-adjustment-section" class="mb-6 hidden">
                    <h4 class="font-medium text-gray-700 mb-2">调整下一次循环间隔：</h4>
                    <div class="flex flex-col space-y-3">
                        <label class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                            <input type="radio" name="cycle-adjustment" value="shorten" class="form-radio text-red-500">
                            <div class="ml-3">
                                <span class="font-medium">缩短50%（记忆模糊）</span>
                                <p class="text-sm text-gray-500">下次间隔变为当前的一半</p>
                            </div>
                        </label>
                        <label class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                            <input type="radio" name="cycle-adjustment" value="keep" class="form-radio text-blue-500" checked>
                            <div class="ml-3">
                                <span class="font-medium">保持不变（记忆一般）</span>
                                <p class="text-sm text-gray-500">按原周期复习</p>
                            </div>
                        </label>
                        <label class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                            <input type="radio" name="cycle-adjustment" value="extend" class="form-radio text-green-500">
                            <div class="ml-3">
                                <span class="font-medium">延长20%（记忆清晰）</span>
                                <p class="text-sm text-gray-500">下次间隔增加当前的20%</p>
                            </div>
                        </label>
                    </div>
                </div>
                
                <div class="mb-6">
                    <label class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                        <input type="checkbox" id="mark-permanent" class="form-checkbox text-primary">
                        <div class="ml-3">
                            <span class="font-medium">标记为永久掌握（停止循环）</span>
                            <p class="text-sm text-gray-500">确认已完全掌握，无需再复习</p>
                        </div>
                    </label>
                </div>
            </div>
            <div class="p-6 border-t flex justify-end">
                <button id="complete-review" class="bg-primary hover:bg-indigo-700 text-white px-6 py-2 rounded-lg font-medium transition-custom">
                    完成复习
                </button>
            </div>
        </div>
    </div>

    <!-- 编辑模态框 -->
    <div id="edit-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div class="p-6 border-b">
                <div class="flex justify-between items-center">
                    <h3 class="text-xl font-bold">编辑学习内容</h3>
                    <button id="close-edit-modal" class="text-gray-500 hover:text-gray-700">
                        <i class="fa fa-times text-xl"></i>
                    </button>
                </div>
            </div>
            <div class="p-6">
                <form id="edit-content-form">
                    <div class="mb-4">
                        <label for="edit-content-title" class="block text-sm font-medium text-gray-700 mb-2">标题 *</label>
                        <input type="text" id="edit-content-title" name="title" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                               placeholder="输入学习内容的标题">
                    </div>

                    <div class="mb-4">
                        <label for="edit-content-category" class="block text-sm font-medium text-gray-700 mb-2">类别 *</label>
                        <select id="edit-content-category" name="category" required
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                            <option value="">选择类别</option>
                            <option value="math">数学</option>
                            <option value="computer">计算机</option>
                            <option value="english">英语</option>
                            <option value="politics">政治</option>
                            <option value="other">其他</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label for="edit-content-description" class="block text-sm font-medium text-gray-700 mb-2">详细内容 *</label>
                        <textarea id="edit-content-description" name="description" required rows="6"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                                  placeholder="详细描述学习内容，包括关键概念、公式、要点等"></textarea>
                    </div>

                    <div class="mb-4">
                        <label for="edit-content-connections" class="block text-sm font-medium text-gray-700 mb-2">关联的旧知识</label>
                        <textarea id="edit-content-connections" name="connections" rows="3"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                                  placeholder="这个知识点与哪些已学过的内容相关？（可选）"></textarea>
                    </div>

                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-3">难度等级 *</label>
                        <div class="flex flex-col space-y-2">
                            <label class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                                <input type="radio" name="edit-difficulty" value="easy" class="form-radio text-green-500">
                                <div class="ml-3">
                                    <span class="font-medium text-green-700">简单</span>
                                    <p class="text-sm text-gray-500">容易理解和记忆的内容</p>
                                </div>
                            </label>
                            <label class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                                <input type="radio" name="edit-difficulty" value="medium" class="form-radio text-yellow-500">
                                <div class="ml-3">
                                    <span class="font-medium text-yellow-700">中等</span>
                                    <p class="text-sm text-gray-500">需要一定理解和练习的内容</p>
                                </div>
                            </label>
                            <label class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                                <input type="radio" name="edit-difficulty" value="hard" class="form-radio text-red-500">
                                <div class="ml-3">
                                    <span class="font-medium text-red-700">困难</span>
                                    <p class="text-sm text-gray-500">复杂难懂，需要反复学习的内容</p>
                                </div>
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="p-6 border-t flex justify-end space-x-3">
                <button type="button" id="cancel-edit" class="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 font-medium transition-custom">
                    取消
                </button>
                <button type="button" id="save-edit" class="bg-primary hover:bg-indigo-700 text-white px-6 py-2 rounded-lg font-medium transition-custom">
                    保存修改
                </button>
            </div>
        </div>
    </div>

    <!-- 通知提示 -->
    <div id="notification" class="fixed bottom-4 right-4 bg-dark text-white px-6 py-3 rounded-lg shadow-lg transform translate-y-20 opacity-0 transition-all duration-300 flex items-center z-50">
        <i id="notification-icon" class="fa fa-check-circle mr-2"></i>
        <span id="notification-message"></span>
    </div>

    <!-- 页脚 -->
    <footer class="bg-dark text-white py-8">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="mb-4 md:mb-0">
                    <div class="flex items-center space-x-2">
                        <i class="fa fa-brain text-primary text-xl"></i>
                        <span class="font-bold text-lg">记忆强化</span>
                    </div>
                    <p class="text-gray-400 text-sm mt-1">基于科学的间隔复习工具</p>
                </div>
                <div class="flex space-x-6">
                    <a href="#" class="text-gray-400 hover:text-white transition-custom">
                        <i class="fa fa-github text-xl"></i>
                    </a>
                    <a href="#" class="text-gray-400 hover:text-white transition-custom">
                        <i class="fa fa-twitter text-xl"></i>
                    </a>
                    <a href="#" class="text-gray-400 hover:text-white transition-custom">
                        <i class="fa fa-envelope text-xl"></i>
                    </a>
                </div>
            </div>
            <div class="border-t border-gray-800 mt-6 pt-6 text-center text-gray-400 text-sm">
                &copy; <span id="current-year"></span> 记忆强化 - 让学习更高效
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.8/dist/chart.umd.min.js"></script>
    <script>
        // 初始化日期和年份
        document.addEventListener('DOMContentLoaded', function() {
            // 设置当前日期
            const now = new Date();
            const options = { year: 'numeric', month: 'long', day: 'numeric', weekday: 'long' };
            document.getElementById('current-date').textContent = now.toLocaleDateString('zh-CN', options);
            document.getElementById('current-year').textContent = now.getFullYear();
            
            // 移动端菜单切换
            document.getElementById('menu-toggle').addEventListener('click', function() {
                const mobileMenu = document.getElementById('mobile-menu');
                mobileMenu.classList.toggle('hidden');
            });
            
            // 初始化学习内容存储
            if (!localStorage.getItem('learningContents')) {
                localStorage.setItem('learningContents', JSON.stringify([]));
            }
            
            // 渲染学习内容
            renderAllContents();
            renderTodayReviews();
            updateDashboardStats();
            
            // 初始化遗忘曲线图表
            initForgettingCurveChart();
            
            // 表单提交事件
            document.getElementById('content-form').addEventListener('submit', function(e) {
                e.preventDefault();
                saveNewContent();
            });
            
            // 搜索和筛选事件
            document.getElementById('search-content').addEventListener('input', renderAllContents);
            document.getElementById('filter-category').addEventListener('change', renderAllContents);
            
            // 复习模态框事件
            document.getElementById('close-modal').addEventListener('click', closeReviewModal);
            document.getElementById('complete-review').addEventListener('click', completeReview);

            // 编辑模态框事件
            document.getElementById('close-edit-modal').addEventListener('click', closeEditModal);
            document.getElementById('cancel-edit').addEventListener('click', closeEditModal);
            document.getElementById('save-edit').addEventListener('click', saveEditContent);
        });
        
        // 初始化遗忘曲线图表
        function initForgettingCurveChart() {
            const ctx = document.getElementById('forgetting-curve').getContext('2d');
            
            // 模拟遗忘曲线数据（含循环阶段）
            const days = [0, 1, 3, 7, 14, 28, 58, 88, 118]; // 包含循环阶段
            const retentionWithoutReview = [100, 40, 25, 15, 10, 8, 6, 5, 4]; // 无复习的记忆保留率
            const retentionWithReview = [100, 80, 75, 70, 65, 63, 61, 60, 59]; // 有循环复习的记忆保留率
            
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: days.map(d => d === 0 ? '学习时' : `${d}天`),
                    datasets: [
                        {
                            label: '无复习',
                            data: retentionWithoutReview,
                            borderColor: '#EF4444',
                            backgroundColor: 'rgba(239, 68, 68, 0.1)',
                            tension: 0.4,
                            fill: true
                        },
                        {
                            label: '有循环复习',
                            data: retentionWithReview,
                            borderColor: '#10B981',
                            backgroundColor: 'rgba(16, 185, 129, 0.1)',
                            tension: 0.4,
                            fill: true
                        }
                    ]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: '记忆保留率对比（含循环复习）'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return `${context.dataset.label}: ${context.raw}%`;
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            title: {
                                display: true,
                                text: '记忆保留率 (%)'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: '时间'
                            }
                        }
                    }
                }
            });
        }
        
        // 保存新的学习内容
        function saveNewContent() {
            const title = document.getElementById('content-title').value;
            const category = document.getElementById('content-category').value;
            const description = document.getElementById('content-description').value;
            const connections = document.getElementById('content-connections').value;
            const difficultyElements = document.getElementsByName('difficulty');
            let difficulty = 'medium';
            
            // 获取选中的难度
            for (const el of difficultyElements) {
                if (el.checked) {
                    difficulty = el.value;
                    break;
                }
            }
            
            // 生成ID
            const id = Date.now().toString();
            
            // 获取当前时间
            const now = new Date();
            const createdAt = now.toISOString();
            
            // 初始强化阶段的复习间隔（1天、3天、7天、14天）
            const initialIntervals = [
                1 * 24 * 60 * 60 * 1000,   // 1天
                3 * 24 * 60 * 60 * 1000,   // 3天
                7 * 24 * 60 * 60 * 1000,   // 7天
                14 * 24 * 60 * 60 * 1000   // 14天
            ];
            
            // 计算初始强化阶段的复习时间点
            const reviews = initialIntervals.map(interval => {
                const reviewTime = new Date(now.getTime() + interval);
                return {
                    time: reviewTime.toISOString(),
                    completed: false,
                    isCycle: false // 标记为初始强化阶段
                };
            });
            
            // 创建新内容对象
            const newContent = {
                id,
                title,
                category,
                description,
                connections,
                difficulty,
                createdAt,
                reviews,
                inCycle: false, // 是否进入循环阶段
                cycleInterval: null, // 循环间隔（天）
                permanentlyMastered: false // 是否永久掌握
            };
            
            // 保存到本地存储
            const contents = JSON.parse(localStorage.getItem('learningContents'));
            contents.push(newContent);
            localStorage.setItem('learningContents', JSON.stringify(contents));
            
            // 重置表单
            document.getElementById('content-form').reset();
            
            // 更新UI
            renderAllContents();
            renderTodayReviews();
            updateDashboardStats();
            
            // 显示通知
            showNotification('学习内容已保存！', 'success');
        }
        
        // 创建循环复习计划（在完成14天复习后调用）
        function createCyclePlan(content) {
            // 根据难度确定首次循环间隔和固定周期
            let firstCycleInterval, baseCycleInterval;
            
            switch(content.difficulty) {
                case 'hard':
                    firstCycleInterval = 14; // 14天后+14天=28天首次循环
                    baseCycleInterval = 30;  // 后续30天循环
                    break;
                case 'medium':
                    firstCycleInterval = 21; // 14天后+21天=35天首次循环
                    baseCycleInterval = 45;  // 后续45天循环
                    break;
                case 'easy':
                    firstCycleInterval = 28; // 14天后+28天=42天首次循环
                    baseCycleInterval = 60;  // 后续60天循环
                    break;
            }
            
            // 获取最后一次复习时间（14天节点）
            const lastReviewTime = new Date(content.reviews[content.reviews.length - 1].time);
            
            // 生成首次循环时间
            const firstCycleTime = new Date(lastReviewTime.getTime() + firstCycleInterval * 24 * 60 * 60 * 1000);
            content.reviews.push({
                time: firstCycleTime.toISOString(),
                completed: false,
                isCycle: true // 标记为循环复习
            });
            
            // 更新内容状态为循环中
            content.inCycle = true;
            content.cycleInterval = baseCycleInterval;
            
            return content;
        }
        
        // 调整循环间隔
        function adjustCycleInterval(content, adjustment) {
            // 找到当前循环复习项
            const currentCycleIndex = content.reviews.findIndex(item => item.isCycle && !item.completed);
            if (currentCycleIndex === -1) return content;
            
            const currentCycle = content.reviews[currentCycleIndex];
            const currentTime = new Date(currentCycle.time);
            let newInterval = content.cycleInterval;
            
            // 根据调整类型修改间隔
            switch(adjustment) {
                case 'shorten':
                    newInterval = Math.max(15, Math.round(newInterval * 0.5)); // 最短不低于15天
                    break;
                case 'extend':
                    newInterval = Math.min(90, Math.round(newInterval * 1.2)); // 最长不超过90天
                    break;
                case 'keep':
                    // 保持不变
                    break;
            }
            
            // 标记当前循环为已完成
            currentCycle.completed = true;
            
            // 生成下一次循环时间
            const nextCycleTime = new Date(currentTime.getTime() + newInterval * 24 * 60 * 60 * 1000);
            content.reviews.push({
                time: nextCycleTime.toISOString(),
                completed: false,
                isCycle: true
            });
            
            // 更新循环间隔
            content.cycleInterval = newInterval;
            
            return content;
        }
        
        // 渲染所有学习内容
        function renderAllContents() {
            const contents = JSON.parse(localStorage.getItem('learningContents')) || [];
            const contentList = document.getElementById('content-list');
            const searchTerm = document.getElementById('search-content').value.toLowerCase();
            const filterCategory = document.getElementById('filter-category').value;
            
            // 过滤内容
            let filteredContents = contents.filter(content => {
                if (content.permanentlyMastered) return false; // 不显示已永久掌握的内容
                
                const matchesSearch = content.title.toLowerCase().includes(searchTerm) || 
                                     (content.description && content.description.toLowerCase().includes(searchTerm)) ||
                                     (content.connections && content.connections.toLowerCase().includes(searchTerm));
                const matchesCategory = filterCategory === 'all' || content.category === filterCategory;
                return matchesSearch && matchesCategory;
            });
            
            // 按创建时间排序（最新的在前）
            filteredContents.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
            
            // 清空列表
            contentList.innerHTML = '';
            
            // 显示无内容消息或渲染内容
            if (filteredContents.length === 0) {
                contentList.innerHTML = `
                    <div class="text-center py-8 text-gray-500 col-span-full">
                        <i class="fa fa-search text-4xl mb-4 text-gray-300"></i>
                        <p>没有找到匹配的学习内容</p>
                    </div>
                `;
                return;
            }
            
            // 渲染每个内容项
            filteredContents.forEach(content => {
                const categoryNames = {
                    'math': '数学',
                    'computer': '计算机',
                    'english': '英语',
                    'politics': '政治',
                    'other': '其他'
                };
                
                // 计算下次复习时间
                let nextReviewDate = null;
                let isCycleReview = false;
                for (const review of content.reviews) {
                    if (!review.completed) {
                        nextReviewDate = new Date(review.time);
                        isCycleReview = review.isCycle;
                        break;
                    }
                }
                
                // 格式化日期
                let nextReviewText = '已完成所有复习';
                let nextReviewDateFormatted = '';
                if (nextReviewDate) {
                    const now = new Date();
                    const diffInDays = Math.ceil((nextReviewDate - now) / (1000 * 60 * 60 * 24));
                    
                    // 格式化日期为可读性强的格式
                    const options = { year: 'numeric', month: 'short', day: 'numeric' };
                    nextReviewDateFormatted = nextReviewDate.toLocaleDateString('zh-CN', options);
                    
                    if (diffInDays < 0) {
                        nextReviewText = '需要复习';
                    } else if (diffInDays === 0) {
                        nextReviewText = '今天复习';
                    } else if (diffInDays === 1) {
                        nextReviewText = '明天复习';
                    } else {
                        nextReviewText = `${diffInDays}天后复习`;
                    }
                }
                
                // 创建内容卡片
                const contentCard = document.createElement('div');
                contentCard.className = `border rounded-lg overflow-hidden hover:shadow-lg transition-custom ${content.inCycle ? 'border-purple-200' : ''}`;
                
                // 循环状态标签
                const cycleBadge = content.inCycle ? 
                    `<span class="bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full ml-2">${content.cycleInterval}天循环中</span>` : '';
                
                contentCard.innerHTML = `
                    <div class="p-4">
                        <div class="flex flex-wrap justify-between items-start mb-2">
                            <h3 class="font-bold text-lg">${content.title}</h3>
                            <div class="flex">
                                ${cycleBadge}
                            </div>
                        </div>
                        <p class="text-sm text-gray-500 mb-3">
                            <span class="inline-block bg-gray-100 px-2 py-0.5 rounded text-xs mr-2">${categoryNames[content.category]}</span>
                            <span class="inline-block bg-gray-100 px-2 py-0.5 rounded text-xs">
                                ${nextReviewText}
                            </span>
                        </p>
                        ${nextReviewDateFormatted ? `
                            <p class="text-xs text-blue-600 mb-3">
                                <i class="fa fa-calendar-o mr-1"></i>下次复习时间: ${nextReviewDateFormatted}
                            </p>
                        ` : ''}
                        <p class="text-gray-600 text-sm mb-3 line-clamp-2">${content.description || '无详细描述'}</p>
                        ${content.connections ? `
                            <p class="text-gray-600 text-sm mb-4 text-indigo-700 italic line-clamp-1">
                                <i class="fa fa-link mr-1"></i>关联: ${content.connections}
                            </p>
                        ` : ''}
                        <div class="flex justify-end space-x-2">
                            <button class="text-primary hover:text-indigo-700 text-sm" onclick="openReviewModal('${content.id}')">
                                <i class="fa fa-refresh mr-1"></i>复习
                            </button>
                            <button class="text-green-600 hover:text-green-700 text-sm" onclick="openEditModal('${content.id}')">
                                <i class="fa fa-edit mr-1"></i>编辑
                            </button>
                            <button class="text-red-500 hover:text-red-700 text-sm" onclick="deleteContent('${content.id}')">
                                <i class="fa fa-trash mr-1"></i>删除
                            </button>
                        </div>
                    </div>
                `;
                
                contentList.appendChild(contentCard);
            });
        }
        
        // 渲染今日复习内容
        function renderTodayReviews() {
            const contents = JSON.parse(localStorage.getItem('learningContents')) || [];
            const todayReviewsContainer = document.getElementById('today-reviews');
            const noReviewMessage = document.getElementById('no-review-message');
            
            // 获取今天的开始和结束时间
            const now = new Date();
            const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
            const todayEnd = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
            
            // 筛选今天需要复习的内容
            const todayReviews = [];
            
            contents.forEach(content => {
                // 跳过已永久掌握的内容
                if (content.permanentlyMastered) return;
                
                // 检查是否有今天需要完成的未完成复习
                content.reviews.forEach((review, index) => {
                    if (!review.completed) {
                        const reviewDate = new Date(review.time);
                        // 如果复习时间在今天范围内，或者已经过期但未完成
                        if ((reviewDate >= todayStart && reviewDate < todayEnd) || reviewDate < now) {
                            todayReviews.push({
                                ...content,
                                reviewIndex: index,
                                isCycleReview: review.isCycle
                            });
                            return false; // 只添加一次
                        }
                    }
                });
            });
            
            // 按复习时间排序（最早的在前）
            todayReviews.sort((a, b) => new Date(a.reviews[a.reviewIndex].time) - new Date(b.reviews[b.reviewIndex].time));
            
            // 清空容器
            todayReviewsContainer.innerHTML = '';
            
            // 显示无复习消息或渲染复习内容
            if (todayReviews.length === 0) {
                todayReviewsContainer.appendChild(noReviewMessage);
                return;
            }
            
            // 渲染每个复习项
            todayReviews.forEach(item => {
                const categoryNames = {
                    'math': '数学',
                    'computer': '计算机',
                    'english': '英语',
                    'politics': '政治',
                    'other': '其他'
                };
                
                // 计算复习状态（是否已过期）
                const reviewDate = new Date(item.reviews[item.reviewIndex].time);
                const isOverdue = reviewDate < now;
                
                // 格式化复习日期
                const options = { month: 'short', day: 'numeric' };
                const reviewDateFormatted = reviewDate.toLocaleDateString('zh-CN', options);
                
                // 循环复习标记
                const cycleLabel = item.isCycleReview ? 
                    `<span class="inline-block bg-purple-100 text-purple-800 px-2 py-0.5 rounded text-xs ml-2">循环复习</span>` : '';
                
                const reviewItem = document.createElement('div');
                reviewItem.className = `border rounded-lg p-4 hover:shadow-md transition-custom ${isOverdue ? 'border-red-200 bg-red-50' : ''}`;
                reviewItem.innerHTML = `
                    <div class="flex justify-between items-start mb-3">
                        <h3 class="font-bold">${item.title}</h3>
                        <span class="text-sm px-2 py-0.5 rounded-full ${isOverdue ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800'}">
                            ${isOverdue ? '已过期' : '今日复习'}
                        </span>
                    </div>
                    <p class="text-sm text-gray-500 mb-3">
                        <span class="inline-block bg-gray-100 px-2 py-0.5 rounded text-xs mr-2">${categoryNames[item.category]}</span>
                        <span class="inline-block bg-gray-100 px-2 py-0.5 rounded text-xs mr-2">复习次数: ${item.reviewIndex + 1}/${item.reviews.length}</span>
                        ${cycleLabel}
                        <span class="text-xs text-gray-500">复习日期: ${reviewDateFormatted}</span>
                    </p>
                    ${item.connections ? `
                        <p class="text-indigo-700 text-sm mb-3 italic line-clamp-1">
                            <i class="fa fa-link mr-1"></i>关联: ${item.connections}
                        </p>
                    ` : ''}
                    <p class="text-gray-600 text-sm mb-4 line-clamp-2">${item.description || '无详细描述'}</p>
                    <button class="bg-primary hover:bg-indigo-700 text-white px-4 py-2 rounded-lg text-sm transition-custom" onclick="openReviewModal('${item.id}')">
                        <i class="fa fa-check mr-1"></i>开始复习
                    </button>
                `;
                
                todayReviewsContainer.appendChild(reviewItem);
            });
        }
        
        // 更新仪表盘统计
        function updateDashboardStats() {
            const contents = JSON.parse(localStorage.getItem('learningContents')) || [];
            const now = new Date();
            const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
            const todayEnd = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
            
            // 计算今日复习数量
            let todayReviewCount = 0;
            // 计算处于循环阶段的内容数量
            let cyclingContentCount = 0;
            
            contents.forEach(content => {
                // 跳过已永久掌握的内容
                if (content.permanentlyMastered) return;
                
                // 统计处于循环阶段的内容
                if (content.inCycle) {
                    cyclingContentCount++;
                }
                
                // 统计今日需要复习的内容
                content.reviews.forEach(review => {
                    if (!review.completed) {
                        const reviewDate = new Date(review.time);
                        if ((reviewDate >= todayStart && reviewDate < todayEnd) || reviewDate < now) {
                            todayReviewCount++;
                            return false;
                        }
                    }
                });
            });
            
            // 计算总学习内容
            const totalContentCount = contents.filter(content => !content.permanentlyMastered).length;
            
            // 更新UI
            document.getElementById('today-review-count').textContent = todayReviewCount;
            document.getElementById('total-content-count').textContent = totalContentCount;
            document.getElementById('cycling-content-count').textContent = cyclingContentCount;
        }
        
        // 打开复习模态框
        let currentReviewId = null;
        function openReviewModal(contentId) {
            currentReviewId = contentId;
            const contents = JSON.parse(localStorage.getItem('learningContents')) || [];
            const content = contents.find(item => item.id === contentId);
            
            if (!content) return;
            
            const categoryNames = {
                'math': '数学',
                'computer': '计算机',
                'english': '英语',
                'politics': '政治',
                'other': '其他'
            };
            
            // 找到下一次需要复习的索引
            let nextReviewIndex = -1;
            let isCycleReview = false;
            for (let i = 0; i < content.reviews.length; i++) {
                if (!content.reviews[i].completed) {
                    nextReviewIndex = i;
                    isCycleReview = content.reviews[i].isCycle;
                    break;
                }
            }
            
            // 更新模态框内容
            document.getElementById('modal-title').textContent = content.title;
            document.getElementById('modal-category').textContent = `类别: ${categoryNames[content.category]} | 复习: ${nextReviewIndex > -1 ? `${nextReviewIndex + 1}/${content.reviews.length}` : '已完成所有复习'}`;
            document.getElementById('modal-description').textContent = content.description || '无详细描述';
            document.getElementById('modal-connections').textContent = content.connections || '无关联的旧知识';
            
            // 显示或隐藏循环调整部分
            const cycleAdjustmentSection = document.getElementById('cycle-adjustment-section');
            if (isCycleReview) {
                cycleAdjustmentSection.classList.remove('hidden');
                // 默认选中"保持不变"
                document.querySelector('input[name="cycle-adjustment"][value="keep"]').checked = true;
            } else {
                cycleAdjustmentSection.classList.add('hidden');
            }
            
            // 重置永久掌握复选框
            document.getElementById('mark-permanent').checked = false;
            
            // 显示模态框
            document.getElementById('review-modal').classList.remove('hidden');
            
            // 重置单选按钮
            document.querySelector('input[name="memory-level"][value="forgot"]').checked = true;
        }
        
        // 关闭复习模态框
        function closeReviewModal() {
            document.getElementById('review-modal').classList.add('hidden');
            currentReviewId = null;
        }
        
        // 完成复习
        function completeReview() {
            if (!currentReviewId) return;
            
            const contents = JSON.parse(localStorage.getItem('learningContents')) || [];
            const contentIndex = contents.findIndex(item => item.id === currentReviewId);
            
            if (contentIndex === -1) {
                closeReviewModal();
                return;
            }
            
            // 检查是否标记为永久掌握
            const markPermanent = document.getElementById('mark-permanent').checked;
            if (markPermanent) {
                contents[contentIndex].permanentlyMastered = true;
                localStorage.setItem('learningContents', JSON.stringify(contents));
                closeReviewModal();
                renderAllContents();
                renderTodayReviews();
                updateDashboardStats();
                showNotification('已标记为永久掌握，将不再参与复习', 'success');
                return;
            }
            
            // 获取用户选择的记忆程度
            const memoryLevel = document.querySelector('input[name="memory-level"]:checked').value;
            
            // 找到当前需要复习的索引
            let currentReviewIndex = -1;
            let isCycleReview = false;
            for (let i = 0; i < contents[contentIndex].reviews.length; i++) {
                if (!contents[contentIndex].reviews[i].completed) {
                    currentReviewIndex = i;
                    isCycleReview = contents[contentIndex].reviews[i].isCycle;
                    break;
                }
            }
            
            // 如果还有未完成的复习，标记为已完成
            if (currentReviewIndex > -1) {
                contents[contentIndex].reviews[currentReviewIndex].completed = true;
                
                // 检查是否是最后一次初始强化复习（14天节点），如果是则创建循环计划
                const totalInitialReviews = 4; // 1天、3天、7天、14天
                if (currentReviewIndex === totalInitialReviews - 1 && !contents[contentIndex].inCycle) {
                    // 创建循环计划
                    contents[contentIndex] = createCyclePlan(contents[contentIndex]);
                } 
                // 如果是循环复习，则根据调整参数更新下一次循环
                else if (isCycleReview) {
                    const adjustment = document.querySelector('input[name="cycle-adjustment"]:checked').value;
                    contents[contentIndex] = adjustCycleInterval(contents[contentIndex], adjustment);
                }
                // 初始强化阶段的后续复习（非最后一次）
                else if (currentReviewIndex < totalInitialReviews - 1) {
                    // 根据记忆程度调整下一次复习时间
                    if (currentReviewIndex < contents[contentIndex].reviews.length - 1) {
                        const currentReviewTime = new Date(contents[contentIndex].reviews[currentReviewIndex].time);
                        let nextReviewTime = new Date(contents[contentIndex].reviews[currentReviewIndex + 1].time);
                        
                        if (memoryLevel === 'forgot') {
                            // 记忆较差，缩短下次复习间隔
                            nextReviewTime = new Date(currentReviewTime.getTime() + (nextReviewTime - currentReviewTime) * 0.5);
                        } else if (memoryLevel === 'mastered') {
                            // 记忆很好，延长下次复习间隔
                            nextReviewTime = new Date(currentReviewTime.getTime() + (nextReviewTime - currentReviewTime) * 1.5);
                        }
                        
                        contents[contentIndex].reviews[currentReviewIndex + 1].time = nextReviewTime.toISOString();
                    }
                }
            }
            
            // 保存更新
            localStorage.setItem('learningContents', JSON.stringify(contents));
            
            // 关闭模态框
            closeReviewModal();
            
            // 更新UI
            renderAllContents();
            renderTodayReviews();
            updateDashboardStats();
            
            // 显示通知
            showNotification('复习已完成！', 'success');
        }

        // 编辑功能相关
        let currentEditId = null;

        // 打开编辑模态框
        function openEditModal(contentId) {
            currentEditId = contentId;
            const contents = JSON.parse(localStorage.getItem('learningContents')) || [];
            const content = contents.find(item => item.id === contentId);

            if (!content) return;

            // 填充表单数据
            document.getElementById('edit-content-title').value = content.title;
            document.getElementById('edit-content-category').value = content.category;
            document.getElementById('edit-content-description').value = content.description;
            document.getElementById('edit-content-connections').value = content.connections || '';

            // 设置难度等级
            const difficultyRadio = document.querySelector(`input[name="edit-difficulty"][value="${content.difficulty}"]`);
            if (difficultyRadio) {
                difficultyRadio.checked = true;
            }

            // 显示模态框
            document.getElementById('edit-modal').classList.remove('hidden');
        }

        // 关闭编辑模态框
        function closeEditModal() {
            document.getElementById('edit-modal').classList.add('hidden');
            currentEditId = null;
            document.getElementById('edit-content-form').reset();
        }

        // 保存编辑内容
        function saveEditContent() {
            if (!currentEditId) return;

            // 获取表单数据
            const title = document.getElementById('edit-content-title').value.trim();
            const category = document.getElementById('edit-content-category').value;
            const description = document.getElementById('edit-content-description').value.trim();
            const connections = document.getElementById('edit-content-connections').value.trim();

            // 获取难度等级
            const difficultyElements = document.getElementsByName('edit-difficulty');
            let difficulty = '';
            for (const element of difficultyElements) {
                if (element.checked) {
                    difficulty = element.value;
                    break;
                }
            }

            // 验证必填字段
            if (!title || !category || !description || !difficulty) {
                showNotification('请填写所有必填字段', 'error');
                return;
            }

            // 获取现有内容
            const contents = JSON.parse(localStorage.getItem('learningContents')) || [];
            const contentIndex = contents.findIndex(item => item.id === currentEditId);

            if (contentIndex === -1) {
                showNotification('找不到要编辑的内容', 'error');
                return;
            }

            // 更新内容（保持原有的复习数据和其他属性）
            contents[contentIndex] = {
                ...contents[contentIndex], // 保持原有属性
                title,
                category,
                description,
                connections,
                difficulty
            };

            // 保存到本地存储
            localStorage.setItem('learningContents', JSON.stringify(contents));

            // 关闭模态框
            closeEditModal();

            // 更新UI
            renderAllContents();
            renderTodayReviews();
            updateDashboardStats();

            // 显示通知
            showNotification('内容已成功更新！', 'success');
        }

        // 删除学习内容
        function deleteContent(contentId) {
            if (confirm('确定要删除这个学习内容吗？此操作不可恢复。')) {
                let contents = JSON.parse(localStorage.getItem('learningContents')) || [];
                contents = contents.filter(item => item.id !== contentId);
                localStorage.setItem('learningContents', JSON.stringify(contents));
                
                // 更新UI
                renderAllContents();
                renderTodayReviews();
                updateDashboardStats();
                
                // 显示通知
                showNotification('学习内容已删除', 'info');
            }
        }
        
        // 显示通知
        function showNotification(message, type = 'info') {
            const notification = document.getElementById('notification');
            const notificationMessage = document.getElementById('notification-message');
            const notificationIcon = document.getElementById('notification-icon');
            
            // 设置消息和图标
            notificationMessage.textContent = message;
            
            if (type === 'success') {
                notificationIcon.className = 'fa fa-check-circle mr-2 text-green-500';
            } else if (type === 'error') {
                notificationIcon.className = 'fa fa-exclamation-circle mr-2 text-red-500';
            } else {
                notificationIcon.className = 'fa fa-info-circle mr-2 text-blue-500';
            }
            
            // 显示通知
            notification.classList.remove('translate-y-20', 'opacity-0');
            notification.classList.add('translate-y-0', 'opacity-100');
            
            // 3秒后隐藏通知
            setTimeout(() => {
                notification.classList.remove('translate-y-0', 'opacity-100');
                notification.classList.add('translate-y-20', 'opacity-0');
            }, 3000);
        }
    </script>
</body>
</html>
