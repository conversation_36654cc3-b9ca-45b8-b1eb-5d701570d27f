<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支出记录 - 生活NOTE</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <!-- 引入Chart.js用于统计图表 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.8/dist/chart.umd.min.js"></script>
    <!-- 引入html2canvas用于生成图片 -->
    <script src="https://cdn.jsdelivr.net/npm/html2canvas@1.4.1/dist/html2canvas.min.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#4F46E5',
                        secondary: '#10B981',
                        accent: '#F59E0B',
                        light: '#F5F7FF',
                        dark: '#1E293B'
                    }
                }
            }
        }
    </script>
    <style>
        .nav-link {
            color: #6B7280;
            font-weight: 500;
            transition: all 0.2s ease;
            padding: 0.5rem 0;
            position: relative;
            display: flex;
            align-items: center;
            text-decoration: none;
        }
        .nav-link:hover { color: #F59E0B; }
        .nav-link.active { color: #F59E0B; font-weight: 600; }
        .nav-link::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: #F59E0B;
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }
        .nav-link:hover::after,
        .nav-link.active::after { transform: scaleX(1); }
        .mobile-nav-link {
            color: #6B7280;
            border-radius: 0.375rem;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            text-decoration: none;
        }
        .mobile-nav-link:hover { color: #3B82F6; background-color: #F3F4F6; }
        .mobile-nav-link.active { color: #3B82F6; background-color: rgba(59, 130, 246, 0.1); font-weight: 600; }
        
        .toast {
            position: fixed;
            bottom: 20px;
            right: 20px;
            padding: 12px 24px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            z-index: 1000;
            transform: translateY(100px);
            opacity: 0;
            transition: all 0.3s ease;
        }
        .toast.show {
            transform: translateY(0);
            opacity: 1;
        }
        .toast.success { background-color: #10B981; }
        .toast.error { background-color: #EF4444; }
    </style>
</head>
<body class="bg-gray-50 text-gray-800">
    <!-- 统一导航栏 -->
    <nav class="bg-white shadow-md sticky top-0 left-0 right-0 z-50 transition-all duration-300">
        <div class="container mx-auto px-4">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="main-plan.html" class="flex items-center">
                        <i class="fa fa-apple text-green-500 text-2xl mr-2"></i>
                        <span class="font-bold text-xl text-gray-800">生活NOTE</span>
                    </a>
                </div>
                <div class="hidden md:flex items-center space-x-8">
                    <a href="main-plan.html" class="nav-link">
                        <i class="fa fa-home mr-2"></i>主计划
                    </a>
                    <a href="expense-tracker.html" class="nav-link active">
                        <i class="fa fa-money mr-2"></i>支出记录
                    </a>
                    <a href="wishlist.html" class="nav-link">
                        <i class="fa fa-star mr-2"></i>愿望清单
                    </a>
                    <a href="settings.html" class="nav-link">
                        <i class="fa fa-cog mr-2"></i>设置
                    </a>
                </div>
                <div class="md:hidden flex items-center">
                    <button id="mobile-menu-button" class="text-gray-600 hover:text-blue-500 focus:outline-none">
                        <i class="fa fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>
        <div id="mobile-menu" class="hidden md:hidden bg-white shadow-lg w-full">
            <div class="px-2 pt-2 pb-3 space-y-1">
                <a href="main-plan.html" class="mobile-nav-link block px-3 py-2 rounded-md">
                    <i class="fa fa-home mr-2"></i>主计划
                </a>
                <a href="expense-tracker.html" class="mobile-nav-link active block px-3 py-2 rounded-md">
                    <i class="fa fa-money mr-2"></i>支出记录
                </a>
                <a href="wishlist.html" class="mobile-nav-link block px-3 py-2 rounded-md">
                    <i class="fa fa-star mr-2"></i>愿望清单
                </a>
                <a href="settings.html" class="mobile-nav-link block px-3 py-2 rounded-md">
                    <i class="fa fa-cog mr-2"></i>设置
                </a>
            </div>
        </div>
    </nav>

    <main class="container mx-auto px-4 py-8" style="padding-top: 2rem;">
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-gray-800 mb-2">
                <i class="fa fa-money text-yellow-500 mr-2"></i>支出记录
            </h1>
            <p class="text-gray-600">记录和管理您的日常支出</p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- 左侧：添加新支出 -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-xl p-6 shadow-md">
                    <h2 class="text-xl font-bold mb-4 flex items-center">
                        <i class="fa fa-plus-circle text-blue-600 mr-2"></i>添加新支出
                    </h2>
                    
                    <form id="expense-form" class="space-y-4">
                        <div>
                            <label for="expense-date" class="block text-sm font-medium text-gray-700 mb-1">日期</label>
                            <input type="date" id="expense-date" 
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                required>
                        </div>
                        
                        <div>
                            <label for="expense-category" class="block text-sm font-medium text-gray-700 mb-1">类别</label>
                            <select id="expense-category" 
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                required>
                                <option value="">请选择类别</option>
                                <option value="餐饮">餐饮</option>
                                <option value="交通">交通</option>
                                <option value="购物">购物</option>
                                <option value="住房">住房</option>
                                <option value="娱乐">娱乐</option>
                                <option value="医疗">医疗</option>
                                <option value="教育">教育</option>
                                <option value="生活">生活</option>
                                <option value="其他">其他</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="expense-amount" class="block text-sm font-medium text-gray-700 mb-1">金额 (¥)</label>
                            <input type="number" id="expense-amount" min="0.01" step="0.01" 
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                placeholder="0.00" required>
                        </div>
                        
                        <div>
                            <label for="expense-note" class="block text-sm font-medium text-gray-700 mb-1">备注 (可选)</label>
                            <textarea id="expense-note" rows="2" 
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                placeholder="添加备注信息..."></textarea>
                        </div>
                        
                        <button type="submit" 
                            class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 flex items-center justify-center">
                            <i class="fa fa-save mr-2"></i>保存记录
                        </button>
                    </form>
                </div>
                
                <!-- 本月概览 -->
                <div class="bg-white rounded-xl p-6 shadow-md mt-6">
                    <h2 class="text-xl font-bold mb-4 flex items-center">
                        <i class="fa fa-bar-chart text-yellow-500 mr-2"></i>本月概览
                    </h2>
                    
                    <div class="space-y-4">
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">总支出</span>
                            <span id="monthly-total" class="font-bold text-lg text-red-600">¥0.00</span>
                        </div>
                        
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">记录数量</span>
                            <span id="monthly-count" class="font-bold text-lg text-blue-600">0 条</span>
                        </div>
                        
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">平均每日</span>
                            <span id="daily-average" class="font-bold text-lg text-green-600">¥0.00</span>
                        </div>
                        
                        <div class="pt-2 border-t border-gray-200">
                            <span class="text-sm text-gray-500 block mb-1">主要支出类别</span>
                            <div id="top-category" class="flex items-center">
                                <span class="text-gray-400">暂无数据</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧：支出列表 -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-xl p-6 shadow-md">
                    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 gap-4">
                        <h2 class="text-xl font-bold flex items-center">
                            <i class="fa fa-list text-green-500 mr-2"></i>支出记录
                        </h2>
                        <div class="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
                            <input type="text" id="search-expenses" placeholder="搜索支出记录..."
                                class="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 flex-1 sm:w-48">
                            <button onclick="generateReport()" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg text-sm whitespace-nowrap">
                                <i class="fa fa-file-text mr-1"></i>网页报告
                            </button>
                            <button onclick="generateImageReport()" class="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg text-sm whitespace-nowrap">
                                <i class="fa fa-image mr-1"></i>图片报告
                            </button>
                            <button onclick="exportData()" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-sm whitespace-nowrap">
                                <i class="fa fa-download mr-1"></i>导出数据
                            </button>
                        </div>
                    </div>
                    
                    <div id="expenses-list" class="space-y-3 min-h-[300px]">
                        <!-- 支出记录将在这里显示 -->
                    </div>
                    
                    <!-- 分页控制 -->
                    <div class="flex justify-between items-center mt-6 pt-4 border-t border-gray-200">
                        <div class="text-sm text-gray-500">
                            显示第 <span id="page-info">1-5</span> 条，共 <span id="total-count">0</span> 条记录
                        </div>
                        <div class="flex space-x-2">
                            <button id="prev-page" class="px-3 py-1 bg-gray-200 hover:bg-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed">
                                <i class="fa fa-chevron-left mr-1"></i>上一页
                            </button>
                            <button id="next-page" class="px-3 py-1 bg-gray-200 hover:bg-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed">
                                下一页<i class="fa fa-chevron-right ml-1"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 统计图表区域 -->
        <div class="mt-8">
            <div class="bg-white rounded-xl p-6 shadow-md">
                <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
                    <h2 class="text-xl font-bold flex items-center">
                        <i class="fa fa-pie-chart text-blue-500 mr-2"></i>支出统计
                    </h2>
                    
                    <div class="flex space-x-2 mt-2 sm:mt-0">
                        <button id="weekly-btn" class="px-4 py-1.5 bg-blue-600 text-white rounded-md text-sm font-medium transition-all">
                            本周
                        </button>
                        <button id="monthly-btn" class="px-4 py-1.5 bg-gray-200 hover:bg-gray-300 rounded-md text-sm font-medium transition-all">
                            本月
                        </button>
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h3 class="text-sm font-medium text-gray-500 mb-3">支出分布</h3>
                        <div class="w-full h-80 flex items-center justify-center">
                            <canvas id="expense-chart"></canvas>
                        </div>
                    </div>

                    <div>
                        <h3 class="text-sm font-medium text-gray-500 mb-3">支出趋势</h3>
                        <div class="w-full h-80 flex items-center justify-center">
                            <canvas id="trend-chart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        class ExpenseManager {
            constructor() {
                this.expenses = JSON.parse(localStorage.getItem('expenses')) || [];
                this.currentPage = 1;
                this.itemsPerPage = 5;
                this.currentView = 'weekly';
                // 英文到中文的类别映射，以及类别统一化
                this.categoryMap = {
                    'food': '餐饮',        // 将食物统一为餐饮
                    '食物': '餐饮',        // 将中文食物也统一为餐饮
                    'transport': '交通',
                    'shopping': '购物',
                    'housing': '住房',
                    'entertainment': '娱乐',
                    'medical': '医疗',
                    'education': '教育',
                    'life': '生活',
                    'other': '其他',
                    'dining': '餐饮'
                };
                this.init();
            }

            init() {
                // 修复现有数据中的英文类别名称
                this.fixExistingCategories();
                this.renderExpenses();
                this.updateCharts();
                this.updateSummary();
                this.setupEventListeners();

                // 设置默认日期为今天
                const dateInput = document.getElementById('expense-date');
                if (dateInput) {
                    dateInput.valueAsDate = new Date();
                }

                console.log('ExpenseManager 初始化完成，当前支出记录数量:', this.expenses.length);
            }

            // 修复现有数据中的类别名称
            fixExistingCategories() {
                let hasChanges = false;
                this.expenses.forEach(expense => {
                    const originalCategory = expense.category;
                    let fixedCategory = this.getCategoryInChinese(expense.category);

                    // 特别处理：将"食物"统一为"餐饮"
                    if (originalCategory === '食物' || originalCategory === 'food') {
                        fixedCategory = '餐饮';
                    }

                    if (originalCategory !== fixedCategory) {
                        expense.category = fixedCategory;
                        hasChanges = true;
                        console.log(`类别名称已修复: ${originalCategory} -> ${fixedCategory}`);
                    }
                });

                if (hasChanges) {
                    this.saveExpenses();
                    console.log('已修复数据中的类别名称，食物类别已统一为餐饮');
                }
            }

            // 获取中文类别名称
            getCategoryInChinese(category) {
                const lowerCategory = category.toLowerCase();
                return this.categoryMap[lowerCategory] || category;
            }

            setupEventListeners() {
                // 表单提交事件
                const form = document.getElementById('expense-form');
                if (form) {
                    form.addEventListener('submit', (e) => {
                        e.preventDefault();
                        this.addExpense();
                    });
                }

                // 搜索功能
                const searchInput = document.getElementById('search-expenses');
                if (searchInput) {
                    searchInput.addEventListener('input', () => {
                        this.currentPage = 1;
                        this.renderExpenses();
                    });
                }

                // 统计视图切换
                this.setupViewToggle();

                // 分页控制
                this.setupPagination();
            }

            setupViewToggle() {
                const weeklyBtn = document.getElementById('weekly-btn');
                const monthlyBtn = document.getElementById('monthly-btn');

                if (weeklyBtn) {
                    weeklyBtn.addEventListener('click', () => {
                        this.currentView = 'weekly';
                        weeklyBtn.classList.add('bg-blue-600', 'text-white');
                        weeklyBtn.classList.remove('bg-gray-200', 'hover:bg-gray-300');
                        monthlyBtn.classList.add('bg-gray-200', 'hover:bg-gray-300');
                        monthlyBtn.classList.remove('bg-blue-600', 'text-white');
                        this.updateCharts();
                    });
                }

                if (monthlyBtn) {
                    monthlyBtn.addEventListener('click', () => {
                        this.currentView = 'monthly';
                        monthlyBtn.classList.add('bg-blue-600', 'text-white');
                        monthlyBtn.classList.remove('bg-gray-200', 'hover:bg-gray-300');
                        weeklyBtn.classList.add('bg-gray-200', 'hover:bg-gray-300');
                        weeklyBtn.classList.remove('bg-blue-600', 'text-white');
                        this.updateCharts();
                    });
                }
            }

            setupPagination() {
                const prevBtn = document.getElementById('prev-page');
                const nextBtn = document.getElementById('next-page');

                if (prevBtn) {
                    prevBtn.addEventListener('click', () => {
                        if (this.currentPage > 1) {
                            this.currentPage--;
                            this.renderExpenses();
                        }
                    });
                }

                if (nextBtn) {
                    nextBtn.addEventListener('click', () => {
                        const filteredExpenses = this.getFilteredExpenses();
                        const totalPages = Math.ceil(filteredExpenses.length / this.itemsPerPage);
                        if (this.currentPage < totalPages) {
                            this.currentPage++;
                            this.renderExpenses();
                        }
                    });
                }
            }

            addExpense() {
                const date = document.getElementById('expense-date').value;
                const category = document.getElementById('expense-category').value;
                const amount = parseFloat(document.getElementById('expense-amount').value);
                const note = document.getElementById('expense-note').value;

                console.log('添加支出:', { date, category, amount, note });

                if (!date || !category || !amount || amount <= 0) {
                    this.showToast('请填写完整的支出信息', 'error');
                    return;
                }

                const newExpense = {
                    id: Date.now().toString(),
                    date,
                    category,
                    amount,
                    note: note || ''
                };

                this.expenses.unshift(newExpense);
                this.saveExpenses();

                // 重置表单
                document.getElementById('expense-form').reset();
                document.getElementById('expense-date').valueAsDate = new Date();

                // 更新UI
                this.renderExpenses();
                this.updateCharts();
                this.updateSummary();

                this.showToast('支出记录已添加', 'success');
                console.log('支出添加成功，当前总数:', this.expenses.length);
            }

            getFilteredExpenses() {
                const searchTerm = document.getElementById('search-expenses')?.value.toLowerCase() || '';

                if (!searchTerm) {
                    return this.expenses;
                }

                return this.expenses.filter(expense =>
                    expense.category.toLowerCase().includes(searchTerm) ||
                    expense.note.toLowerCase().includes(searchTerm) ||
                    expense.amount.toString().includes(searchTerm)
                );
            }

            renderExpenses() {
                const container = document.getElementById('expenses-list');
                if (!container) return;

                const filteredExpenses = this.getFilteredExpenses();
                const totalPages = Math.ceil(filteredExpenses.length / this.itemsPerPage);
                const startIndex = (this.currentPage - 1) * this.itemsPerPage;
                const endIndex = startIndex + this.itemsPerPage;
                const currentExpenses = filteredExpenses.slice(startIndex, endIndex);

                // 更新分页信息
                this.updatePaginationInfo(filteredExpenses, startIndex, endIndex, totalPages);

                if (filteredExpenses.length === 0) {
                    container.innerHTML = '<p class="text-gray-500 text-center py-8">暂无支出记录</p>';
                    return;
                }

                const html = currentExpenses.map(expense => {
                    const displayCategory = this.getCategoryInChinese(expense.category);
                    return `
                    <div class="expense-item flex justify-between items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors" data-expense-id="${expense.id}">
                        <div class="flex-1">
                            <div class="flex items-center mb-1">
                                <select class="expense-category-select text-sm border border-gray-300 rounded px-2 py-1 mr-2" style="display: none;">
                                    <option value="餐饮" ${expense.category === '餐饮' ? 'selected' : ''}>餐饮</option>
                                    <option value="交通" ${expense.category === '交通' ? 'selected' : ''}>交通</option>
                                    <option value="购物" ${expense.category === '购物' ? 'selected' : ''}>购物</option>
                                    <option value="住房" ${expense.category === '住房' ? 'selected' : ''}>住房</option>
                                    <option value="娱乐" ${expense.category === '娱乐' ? 'selected' : ''}>娱乐</option>
                                    <option value="医疗" ${expense.category === '医疗' ? 'selected' : ''}>医疗</option>
                                    <option value="教育" ${expense.category === '教育' ? 'selected' : ''}>教育</option>
                                    <option value="生活" ${expense.category === '生活' ? 'selected' : ''}>生活</option>
                                    <option value="其他" ${expense.category === '其他' ? 'selected' : ''}>其他</option>
                                </select>
                                <span class="expense-category-display font-medium text-gray-800">${displayCategory}</span>
                                <input type="date" class="expense-date-edit text-sm border border-gray-300 rounded px-2 py-1 ml-2" value="${expense.date}" style="display: none;">
                                <span class="expense-date-display text-sm text-gray-500 ml-2">${expense.date}</span>
                            </div>
                            <div class="expense-amount-container flex items-center mb-1">
                                <input type="number" class="expense-amount-edit text-sm border border-gray-300 rounded px-2 py-1 mr-2" value="${expense.amount}" min="0.01" step="0.01" style="display: none;">
                                <span class="expense-amount-display font-bold text-red-600">¥${expense.amount.toFixed(2)}</span>
                            </div>
                            <div class="expense-note-container">
                                <textarea class="expense-note-edit text-sm border border-gray-300 rounded px-2 py-1 w-full" rows="2" style="display: none;">${expense.note || ''}</textarea>
                                ${expense.note ? `<p class="expense-note-display text-sm text-gray-600">${expense.note}</p>` : '<p class="expense-note-display text-sm text-gray-400">无备注</p>'}
                            </div>
                        </div>
                        <div class="text-right flex flex-col space-y-1">
                            <button class="edit-expense-btn text-blue-500 hover:text-blue-700 transition-colors" onclick="expenseManager.toggleEditExpense('${expense.id}')">
                                <i class="fa fa-edit"></i>
                            </button>
                            <button class="save-expense-btn text-green-500 hover:text-green-700 transition-colors" onclick="expenseManager.saveExpenseEdit('${expense.id}')" style="display: none;">
                                <i class="fa fa-check"></i>
                            </button>
                            <button class="cancel-expense-btn text-gray-500 hover:text-gray-700 transition-colors" onclick="expenseManager.cancelExpenseEdit('${expense.id}')" style="display: none;">
                                <i class="fa fa-times"></i>
                            </button>
                            <button onclick="expenseManager.deleteExpense('${expense.id}')"
                                class="text-red-500 hover:text-red-700 transition-colors">
                                <i class="fa fa-trash"></i>
                            </button>
                        </div>
                    </div>
                `;
                }).join('');

                container.innerHTML = html;
            }

            updatePaginationInfo(filteredExpenses, startIndex, endIndex, totalPages) {
                const pageInfo = document.getElementById('page-info');
                const totalCount = document.getElementById('total-count');
                const prevBtn = document.getElementById('prev-page');
                const nextBtn = document.getElementById('next-page');

                if (pageInfo) {
                    const start = filteredExpenses.length > 0 ? startIndex + 1 : 0;
                    const end = Math.min(endIndex, filteredExpenses.length);
                    pageInfo.textContent = `${start}-${end}`;
                }

                if (totalCount) {
                    totalCount.textContent = filteredExpenses.length.toString();
                }

                if (prevBtn) {
                    prevBtn.disabled = this.currentPage <= 1;
                }

                if (nextBtn) {
                    nextBtn.disabled = this.currentPage >= totalPages;
                }
            }

            // 切换编辑模式
            toggleEditExpense(id) {
                const expenseItem = document.querySelector(`[data-expense-id="${id}"]`);
                if (!expenseItem) return;

                const isEditing = expenseItem.querySelector('.expense-category-select').style.display !== 'none';

                if (isEditing) {
                    this.cancelExpenseEdit(id);
                } else {
                    this.enterEditMode(expenseItem);
                }
            }

            // 进入编辑模式
            enterEditMode(expenseItem) {
                // 显示编辑控件
                expenseItem.querySelector('.expense-category-select').style.display = 'inline-block';
                expenseItem.querySelector('.expense-date-edit').style.display = 'inline-block';
                expenseItem.querySelector('.expense-amount-edit').style.display = 'inline-block';
                expenseItem.querySelector('.expense-note-edit').style.display = 'block';

                // 隐藏显示控件
                expenseItem.querySelector('.expense-category-display').style.display = 'none';
                expenseItem.querySelector('.expense-date-display').style.display = 'none';
                expenseItem.querySelector('.expense-amount-display').style.display = 'none';
                expenseItem.querySelector('.expense-note-display').style.display = 'none';

                // 切换按钮
                expenseItem.querySelector('.edit-expense-btn').style.display = 'none';
                expenseItem.querySelector('.save-expense-btn').style.display = 'inline-block';
                expenseItem.querySelector('.cancel-expense-btn').style.display = 'inline-block';
            }

            // 退出编辑模式
            exitEditMode(expenseItem) {
                // 隐藏编辑控件
                expenseItem.querySelector('.expense-category-select').style.display = 'none';
                expenseItem.querySelector('.expense-date-edit').style.display = 'none';
                expenseItem.querySelector('.expense-amount-edit').style.display = 'none';
                expenseItem.querySelector('.expense-note-edit').style.display = 'none';

                // 显示显示控件
                expenseItem.querySelector('.expense-category-display').style.display = 'inline';
                expenseItem.querySelector('.expense-date-display').style.display = 'inline';
                expenseItem.querySelector('.expense-amount-display').style.display = 'inline';
                expenseItem.querySelector('.expense-note-display').style.display = 'block';

                // 切换按钮
                expenseItem.querySelector('.edit-expense-btn').style.display = 'inline-block';
                expenseItem.querySelector('.save-expense-btn').style.display = 'none';
                expenseItem.querySelector('.cancel-expense-btn').style.display = 'none';
            }

            // 保存编辑
            saveExpenseEdit(id) {
                const expenseItem = document.querySelector(`[data-expense-id="${id}"]`);
                if (!expenseItem) return;

                const newCategory = expenseItem.querySelector('.expense-category-select').value;
                const newDate = expenseItem.querySelector('.expense-date-edit').value;
                const newAmount = parseFloat(expenseItem.querySelector('.expense-amount-edit').value);
                const newNote = expenseItem.querySelector('.expense-note-edit').value.trim();

                // 验证数据
                if (!newCategory || !newDate || !newAmount || newAmount <= 0) {
                    this.showToast('请填写完整的支出信息', 'error');
                    return;
                }

                // 更新数据
                const expense = this.expenses.find(e => e.id === id);
                if (expense) {
                    expense.category = newCategory;
                    expense.date = newDate;
                    expense.amount = newAmount;
                    expense.note = newNote;

                    this.saveExpenses();
                    this.renderExpenses();
                    this.updateCharts();
                    this.updateSummary();
                    this.showToast('支出记录已更新', 'success');
                }
            }

            // 取消编辑
            cancelExpenseEdit(id) {
                const expenseItem = document.querySelector(`[data-expense-id="${id}"]`);
                if (!expenseItem) return;

                // 恢复原始值
                const expense = this.expenses.find(e => e.id === id);
                if (expense) {
                    expenseItem.querySelector('.expense-category-select').value = expense.category;
                    expenseItem.querySelector('.expense-date-edit').value = expense.date;
                    expenseItem.querySelector('.expense-amount-edit').value = expense.amount;
                    expenseItem.querySelector('.expense-note-edit').value = expense.note || '';
                }

                this.exitEditMode(expenseItem);
            }

            deleteExpense(id) {
                if (confirm('确定要删除这条支出记录吗？')) {
                    this.expenses = this.expenses.filter(expense => expense.id !== id);
                    this.saveExpenses();
                    this.renderExpenses();
                    this.updateCharts();
                    this.updateSummary();
                    this.showToast('支出记录已删除', 'success');
                }
            }

            saveExpenses() {
                localStorage.setItem('expenses', JSON.stringify(this.expenses));
            }

            updateSummary() {
                const now = new Date();
                const currentMonth = now.getMonth();
                const currentYear = now.getFullYear();

                const monthlyExpenses = this.expenses.filter(expense => {
                    const expenseDate = new Date(expense.date);
                    return expenseDate.getMonth() === currentMonth && expenseDate.getFullYear() === currentYear;
                });

                const total = monthlyExpenses.reduce((sum, expense) => sum + expense.amount, 0);
                const count = monthlyExpenses.length;
                const daysInMonth = new Date(currentYear, currentMonth + 1, 0).getDate();
                const currentDay = now.getDate();
                const average = count > 0 ? total / Math.min(currentDay, daysInMonth) : 0;

                document.getElementById('monthly-total').textContent = `¥${total.toFixed(2)}`;
                document.getElementById('monthly-count').textContent = `${count} 条`;
                document.getElementById('daily-average').textContent = `¥${average.toFixed(2)}`;

                // 更新主要支出类别
                this.updateTopCategory(monthlyExpenses);
            }

            updateTopCategory(monthlyExpenses) {
                const categoryMap = {};
                monthlyExpenses.forEach(expense => {
                    const chineseCategory = this.getCategoryInChinese(expense.category);
                    if (!categoryMap[chineseCategory]) {
                        categoryMap[chineseCategory] = 0;
                    }
                    categoryMap[chineseCategory] += expense.amount;
                });

                const topCategoryEl = document.getElementById('top-category');
                if (Object.keys(categoryMap).length > 0) {
                    let topCategory = Object.keys(categoryMap)[0];
                    let maxAmount = categoryMap[topCategory];

                    for (let category in categoryMap) {
                        if (categoryMap[category] > maxAmount) {
                            topCategory = category;
                            maxAmount = categoryMap[category];
                        }
                    }

                    const percentage = monthlyExpenses.length > 0 ?
                        ((maxAmount / monthlyExpenses.reduce((sum, e) => sum + e.amount, 0)) * 100).toFixed(1) : 0;

                    topCategoryEl.innerHTML = `
                        <span class="font-medium text-gray-800">${topCategory}</span>
                        <span class="text-sm text-gray-500 ml-2">${percentage}%</span>
                    `;
                } else {
                    topCategoryEl.innerHTML = '<span class="text-gray-400">暂无数据</span>';
                }
            }

            updateCharts() {
                const { categoryData, trendData, labels } = this.getCurrentViewData();
                this.updateExpenseChart(categoryData);
                this.updateTrendChart(trendData, labels);
            }

            getCurrentViewData() {
                const now = new Date();
                let startDate, endDate, labels = [];

                if (this.currentView === 'weekly') {
                    // 本周数据
                    const dayOfWeek = now.getDay() || 7;
                    startDate = new Date(now);
                    startDate.setDate(now.getDate() - (dayOfWeek - 1));
                    startDate.setHours(0, 0, 0, 0);

                    endDate = new Date(startDate);
                    endDate.setDate(startDate.getDate() + 6);
                    endDate.setHours(23, 59, 59, 999);

                    labels = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
                } else {
                    // 本月数据
                    startDate = new Date(now.getFullYear(), now.getMonth(), 1);
                    endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0);

                    const daysInMonth = endDate.getDate();
                    for (let i = 1; i <= daysInMonth; i++) {
                        labels.push(i.toString());
                    }
                }

                // 筛选时间范围内的支出
                const filteredExpenses = this.expenses.filter(expense => {
                    const expenseDate = new Date(expense.date);
                    return expenseDate >= startDate && expenseDate <= endDate;
                });

                // 计算类别数据
                const categoryMap = {};
                filteredExpenses.forEach(expense => {
                    const chineseCategory = this.getCategoryInChinese(expense.category);
                    if (!categoryMap[chineseCategory]) {
                        categoryMap[chineseCategory] = 0;
                    }
                    categoryMap[chineseCategory] += expense.amount;
                });

                const categoryData = {
                    labels: Object.keys(categoryMap),
                    data: Object.values(categoryMap)
                };

                // 计算趋势数据
                const trendData = new Array(labels.length).fill(0);

                filteredExpenses.forEach(expense => {
                    const expenseDate = new Date(expense.date);
                    let index;

                    if (this.currentView === 'weekly') {
                        let dayIndex = expenseDate.getDay() || 7;
                        index = dayIndex - 1;
                    } else {
                        index = expenseDate.getDate() - 1;
                    }

                    if (index >= 0 && index < trendData.length) {
                        trendData[index] += expense.amount;
                    }
                });

                return { categoryData, trendData, labels };
            }

            updateExpenseChart(categoryData) {
                const ctx = document.getElementById('expense-chart').getContext('2d');

                if (window.expenseChart) {
                    window.expenseChart.destroy();
                }

                if (categoryData.labels.length === 0) {
                    ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);
                    ctx.font = '14px Arial';
                    ctx.fillStyle = '#9CA3AF';
                    ctx.textAlign = 'center';
                    ctx.fillText('暂无数据', ctx.canvas.width / 2, ctx.canvas.height / 2);
                    return;
                }

                const colors = [
                    '#3B82F6', '#10B981', '#F59E0B', '#EF4444',
                    '#8B5CF6', '#06B6D4', '#84CC16', '#F97316'
                ];

                // 计算总金额用于百分比计算
                const totalAmount = categoryData.data.reduce((sum, amount) => sum + amount, 0);

                window.expenseChart = new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: categoryData.labels,
                        datasets: [{
                            data: categoryData.data,
                            backgroundColor: colors.slice(0, categoryData.labels.length),
                            borderWidth: 2,
                            borderColor: '#ffffff'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: true,
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: {
                                    padding: 20,
                                    usePointStyle: true
                                }
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        const label = context.label || '';
                                        const value = context.parsed;
                                        const percentage = ((value / totalAmount) * 100).toFixed(1);
                                        return `${label}: ¥${value.toFixed(2)} (${percentage}%)`;
                                    }
                                }
                            }
                        }
                    }
                });
            }

            updateTrendChart(trendData, labels) {
                const ctx = document.getElementById('trend-chart').getContext('2d');

                if (window.trendChart) {
                    window.trendChart.destroy();
                }

                window.trendChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: '支出金额',
                            data: trendData,
                            borderColor: '#3B82F6',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            borderWidth: 2,
                            fill: true,
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: true,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    callback: function(value) {
                                        return '¥' + value.toFixed(0);
                                    }
                                }
                            }
                        }
                    }
                });
            }

            showToast(message, type = 'success') {
                const toast = document.createElement('div');
                toast.className = `toast ${type}`;
                toast.textContent = message;
                document.body.appendChild(toast);

                setTimeout(() => toast.classList.add('show'), 100);
                setTimeout(() => {
                    toast.classList.remove('show');
                    setTimeout(() => document.body.removeChild(toast), 300);
                }, 3000);
            }
        }

        // 导出数据功能
        function exportData() {
            const expenses = JSON.parse(localStorage.getItem('expenses')) || [];
            const dataStr = JSON.stringify(expenses, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `支出记录_${new Date().toISOString().split('T')[0]}.json`;
            link.click();
        }

        // 生成可视化报告
        function generateReport() {
            const expenses = JSON.parse(localStorage.getItem('expenses')) || [];

            if (expenses.length === 0) {
                alert('暂无支出记录，无法生成报告');
                return;
            }

            // 让用户选择报告类型和时间范围
            showReportModal();
        }

        // 显示报告配置模态框
        function showReportModal() {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4';
            modal.innerHTML = `
                <div class="bg-white rounded-xl max-w-md w-full p-6">
                    <h3 class="text-xl font-bold mb-4 text-center">生成支出报告</h3>

                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">报告时间范围</label>
                            <select id="report-period" class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                                <option value="current-month">本月</option>
                                <option value="last-month">上月</option>
                                <option value="current-year">今年</option>
                                <option value="all">全部记录</option>
                                <option value="custom">自定义时间</option>
                            </select>
                        </div>

                        <div id="custom-date-range" class="hidden space-y-2">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">开始日期</label>
                                <input type="date" id="start-date" class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">结束日期</label>
                                <input type="date" id="end-date" class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">报告标题</label>
                            <input type="text" id="report-title" placeholder="例如：2024年1月生活费支出报告"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">报告说明</label>
                            <textarea id="report-description" placeholder="可选：添加报告说明或备注"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg h-20 resize-none"></textarea>
                        </div>
                    </div>

                    <div class="flex space-x-3 mt-6">
                        <button onclick="closeReportModal()" class="flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                            取消
                        </button>
                        <button onclick="generateHTMLReport()" class="flex-1 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg">
                            生成报告
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // 设置默认标题
            const now = new Date();
            const defaultTitle = `${now.getFullYear()}年${now.getMonth() + 1}月支出报告`;
            document.getElementById('report-title').value = defaultTitle;

            // 监听时间范围选择变化
            document.getElementById('report-period').addEventListener('change', function() {
                const customRange = document.getElementById('custom-date-range');
                if (this.value === 'custom') {
                    customRange.classList.remove('hidden');
                } else {
                    customRange.classList.add('hidden');
                }
            });

            // 点击模态框外部关闭
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeReportModal();
                }
            });
        }

        // 关闭报告模态框
        function closeReportModal() {
            const modal = document.querySelector('.fixed.inset-0.bg-black');
            if (modal) {
                modal.remove();
            }
        }

        // 生成HTML报告
        function generateHTMLReport() {
            const period = document.getElementById('report-period').value;
            const title = document.getElementById('report-title').value || '支出报告';
            const description = document.getElementById('report-description').value;
            const startDate = document.getElementById('start-date').value;
            const endDate = document.getElementById('end-date').value;

            // 获取并筛选数据
            const allExpenses = JSON.parse(localStorage.getItem('expenses')) || [];
            const filteredExpenses = filterExpensesByPeriod(allExpenses, period, startDate, endDate);

            if (filteredExpenses.length === 0) {
                alert('所选时间范围内没有支出记录');
                return;
            }

            // 生成报告数据
            const reportData = generateReportData(filteredExpenses);

            // 生成HTML内容
            const htmlContent = generateReportHTML(title, description, reportData, filteredExpenses);

            // 创建并下载HTML文件
            const blob = new Blob([htmlContent], { type: 'text/html;charset=utf-8' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `${title.replace(/[^\w\s-]/g, '')}_${new Date().toISOString().split('T')[0]}.html`;
            link.click();

            closeReportModal();
        }

        // 根据时间范围筛选支出
        function filterExpensesByPeriod(expenses, period, startDate, endDate) {
            const now = new Date();

            return expenses.filter(expense => {
                const expenseDate = new Date(expense.date);

                switch (period) {
                    case 'current-month':
                        return expenseDate.getFullYear() === now.getFullYear() &&
                               expenseDate.getMonth() === now.getMonth();

                    case 'last-month':
                        const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
                        return expenseDate.getFullYear() === lastMonth.getFullYear() &&
                               expenseDate.getMonth() === lastMonth.getMonth();

                    case 'current-year':
                        return expenseDate.getFullYear() === now.getFullYear();

                    case 'custom':
                        if (!startDate || !endDate) return false;
                        const start = new Date(startDate);
                        const end = new Date(endDate);
                        return expenseDate >= start && expenseDate <= end;

                    case 'all':
                    default:
                        return true;
                }
            });
        }

        // 生成报告数据统计
        function generateReportData(expenses) {
            const data = {
                totalAmount: 0,
                totalCount: expenses.length,
                categoryStats: {},
                dailyStats: {},
                monthlyStats: {},
                averageDaily: 0,
                maxExpense: null,
                minExpense: null,
                dateRange: {
                    start: null,
                    end: null
                }
            };

            if (expenses.length === 0) return data;

            // 按日期排序
            expenses.sort((a, b) => new Date(a.date) - new Date(b.date));

            data.dateRange.start = expenses[0].date;
            data.dateRange.end = expenses[expenses.length - 1].date;

            expenses.forEach(expense => {
                const amount = parseFloat(expense.amount);
                data.totalAmount += amount;

                // 分类统计
                if (!data.categoryStats[expense.category]) {
                    data.categoryStats[expense.category] = {
                        amount: 0,
                        count: 0,
                        percentage: 0
                    };
                }
                data.categoryStats[expense.category].amount += amount;
                data.categoryStats[expense.category].count += 1;

                // 日期统计
                if (!data.dailyStats[expense.date]) {
                    data.dailyStats[expense.date] = {
                        amount: 0,
                        count: 0
                    };
                }
                data.dailyStats[expense.date].amount += amount;
                data.dailyStats[expense.date].count += 1;

                // 月份统计
                const monthKey = expense.date.substring(0, 7); // YYYY-MM
                if (!data.monthlyStats[monthKey]) {
                    data.monthlyStats[monthKey] = {
                        amount: 0,
                        count: 0
                    };
                }
                data.monthlyStats[monthKey].amount += amount;
                data.monthlyStats[monthKey].count += 1;

                // 最大最小支出
                if (!data.maxExpense || amount > data.maxExpense.amount) {
                    data.maxExpense = expense;
                }
                if (!data.minExpense || amount < data.minExpense.amount) {
                    data.minExpense = expense;
                }
            });

            // 计算分类百分比
            Object.keys(data.categoryStats).forEach(category => {
                data.categoryStats[category].percentage =
                    (data.categoryStats[category].amount / data.totalAmount * 100).toFixed(1);
            });

            // 计算日均支出
            const dayCount = Object.keys(data.dailyStats).length;
            data.averageDaily = dayCount > 0 ? data.totalAmount / dayCount : 0;

            return data;
        }

        // 生成HTML报告内容
        function generateReportHTML(title, description, data, expenses) {
            const categoryColors = {
                '餐饮': '#FF6B6B',
                '交通': '#4ECDC4',
                '购物': '#45B7D1',
                '住房': '#96CEB4',
                '娱乐': '#FFEAA7',
                '医疗': '#DDA0DD',
                '教育': '#98D8C8',
                '生活': '#F7DC6F',
                '其他': '#AED6F1'
            };

            // 排序分类数据
            const sortedCategories = Object.entries(data.categoryStats)
                .sort((a, b) => b[1].amount - a[1].amount);

            const html = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header .subtitle {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .summary-card {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            border-left: 5px solid #667eea;
        }

        .summary-card h3 {
            color: #666;
            font-size: 0.9em;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .summary-card .value {
            font-size: 2em;
            font-weight: bold;
            color: #333;
        }

        .section {
            margin-bottom: 40px;
        }

        .section h2 {
            font-size: 1.5em;
            margin-bottom: 20px;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }

        .category-chart {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }

        .category-list {
            space-y: 15px;
        }

        .category-item {
            display: flex;
            align-items: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
            margin-bottom: 10px;
        }

        .category-color {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 15px;
        }

        .category-info {
            flex: 1;
        }

        .category-name {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .category-details {
            font-size: 0.9em;
            color: #666;
        }

        .category-amount {
            font-size: 1.2em;
            font-weight: bold;
            color: #333;
        }

        .pie-chart {
            width: 300px;
            height: 300px;
            margin: 0 auto;
        }

        .expense-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .expense-table th {
            background: #667eea;
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 500;
        }

        .expense-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
        }

        .expense-table tr:hover {
            background: #f8f9fa;
        }

        .amount {
            font-weight: bold;
            color: #e74c3c;
        }

        .footer {
            background: #f8f9fa;
            padding: 30px;
            text-align: center;
            color: #666;
            border-top: 1px solid #eee;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 10px;
            }

            .header {
                padding: 20px;
            }

            .header h1 {
                font-size: 1.8em;
            }

            .content {
                padding: 20px;
            }

            .summary-grid {
                grid-template-columns: 1fr;
            }

            .category-chart {
                grid-template-columns: 1fr;
            }
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
        }

        .progress-bar {
            background: #e9ecef;
            border-radius: 10px;
            height: 8px;
            margin: 10px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            border-radius: 10px;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>${title}</h1>
            <div class="subtitle">
                ${data.dateRange.start} 至 ${data.dateRange.end}
                ${description ? `<br><br>${description}` : ''}
            </div>
        </div>

        <div class="content">
            <!-- 概览统计 -->
            <div class="summary-grid">
                <div class="summary-card">
                    <h3>总支出</h3>
                    <div class="value amount">¥${data.totalAmount.toFixed(2)}</div>
                </div>
                <div class="summary-card">
                    <h3>支出笔数</h3>
                    <div class="value">${data.totalCount}</div>
                </div>
                <div class="summary-card">
                    <h3>日均支出</h3>
                    <div class="value">¥${data.averageDaily.toFixed(2)}</div>
                </div>
                <div class="summary-card">
                    <h3>支出天数</h3>
                    <div class="value">${Object.keys(data.dailyStats).length}</div>
                </div>
            </div>

            ${data.maxExpense ? `
            <div class="highlight">
                <h3 style="margin-bottom: 10px;">💰 最大单笔支出</h3>
                <p><strong>¥${parseFloat(data.maxExpense.amount).toFixed(2)}</strong> - ${data.maxExpense.category} - ${data.maxExpense.note || '无备注'} (${data.maxExpense.date})</p>
            </div>
            ` : ''}

            <!-- 分类统计 -->
            <div class="section">
                <h2>📊 支出分类统计</h2>
                <div class="category-list">
                    ${sortedCategories.map(([category, stats]) => `
                        <div class="category-item">
                            <div class="category-color" style="background-color: ${categoryColors[category] || '#95a5a6'}"></div>
                            <div class="category-info">
                                <div class="category-name">${category}</div>
                                <div class="category-details">${stats.count} 笔 • ${stats.percentage}%</div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: ${stats.percentage}%; background-color: ${categoryColors[category] || '#95a5a6'}"></div>
                                </div>
                            </div>
                            <div class="category-amount">¥${stats.amount.toFixed(2)}</div>
                        </div>
                    `).join('')}
                </div>
            </div>

            <!-- 详细记录 -->
            <div class="section">
                <h2>📝 详细支出记录</h2>
                <table class="expense-table">
                    <thead>
                        <tr>
                            <th>日期</th>
                            <th>类别</th>
                            <th>金额</th>
                            <th>备注</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${expenses.sort((a, b) => new Date(b.date) - new Date(a.date)).map(expense => `
                            <tr>
                                <td>${expense.date}</td>
                                <td>
                                    <span style="display: inline-block; width: 12px; height: 12px; background-color: ${categoryColors[expense.category] || '#95a5a6'}; border-radius: 50%; margin-right: 8px;"></span>
                                    ${expense.category}
                                </td>
                                <td class="amount">¥${parseFloat(expense.amount).toFixed(2)}</td>
                                <td>${expense.note || '-'}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        </div>

        <div class="footer">
            <p>报告生成时间：${new Date().toLocaleString('zh-CN')}</p>
            <p>由生活NOTE支出管理系统生成</p>
        </div>
    </div>
</body>
</html>`;

            return html;
        }

        // 生成图片报告
        function generateImageReport() {
            const expenses = JSON.parse(localStorage.getItem('expenses')) || [];

            if (expenses.length === 0) {
                alert('暂无支出记录，无法生成报告');
                return;
            }

            // 显示图片报告配置模态框
            showImageReportModal();
        }

        // 显示图片报告配置模态框
        function showImageReportModal() {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4';
            modal.innerHTML = `
                <div class="bg-white rounded-xl max-w-md w-full p-6">
                    <h3 class="text-xl font-bold mb-4 text-center">生成图片报告</h3>

                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">报告时间范围</label>
                            <select id="image-report-period" class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                                <option value="current-month">本月</option>
                                <option value="last-month">上月</option>
                                <option value="current-year">今年</option>
                                <option value="all">全部记录</option>
                                <option value="custom">自定义时间</option>
                            </select>
                        </div>

                        <div id="image-custom-date-range" class="hidden space-y-2">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">开始日期</label>
                                <input type="date" id="image-start-date" class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">结束日期</label>
                                <input type="date" id="image-end-date" class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">报告标题</label>
                            <input type="text" id="image-report-title" placeholder="例如：2024年1月生活费支出报告"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">图片尺寸</label>
                            <select id="image-size" class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                                <option value="mobile">手机适配 (375x1200) - 适合微信分享</option>
                                <option value="tablet">平板适配 (768x1400) - 内容丰富</option>
                                <option value="desktop">桌面适配 (1200x1000) - 高清展示</option>
                                <option value="square">正方形 (900x900) - 朋友圈分享</option>
                            </select>
                            <p class="text-xs text-gray-500 mt-2">💡 图片报告包含统计概览、分类分析和详细消费记录</p>
                        </div>
                    </div>

                    <div class="flex space-x-3 mt-6">
                        <button onclick="closeImageReportModal()" class="flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                            取消
                        </button>
                        <button onclick="generateImageFromHTML()" class="flex-1 px-4 py-2 bg-orange-600 hover:bg-orange-700 text-white rounded-lg">
                            生成图片
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // 设置默认标题
            const now = new Date();
            const defaultTitle = `${now.getFullYear()}年${now.getMonth() + 1}月支出报告`;
            document.getElementById('image-report-title').value = defaultTitle;

            // 监听时间范围选择变化
            document.getElementById('image-report-period').addEventListener('change', function() {
                const customRange = document.getElementById('image-custom-date-range');
                if (this.value === 'custom') {
                    customRange.classList.remove('hidden');
                } else {
                    customRange.classList.add('hidden');
                }
            });

            // 点击模态框外部关闭
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeImageReportModal();
                }
            });
        }

        // 关闭图片报告模态框
        function closeImageReportModal() {
            const modal = document.querySelector('.fixed.inset-0.bg-black');
            if (modal) {
                modal.remove();
            }
        }

        // 从HTML生成图片
        function generateImageFromHTML() {
            const period = document.getElementById('image-report-period').value;
            const title = document.getElementById('image-report-title').value || '支出报告';
            const startDate = document.getElementById('image-start-date').value;
            const endDate = document.getElementById('image-end-date').value;
            const imageSize = document.getElementById('image-size').value;

            // 获取并筛选数据
            const allExpenses = JSON.parse(localStorage.getItem('expenses')) || [];
            const filteredExpenses = filterExpensesByPeriod(allExpenses, period, startDate, endDate);

            if (filteredExpenses.length === 0) {
                alert('所选时间范围内没有支出记录');
                return;
            }

            // 生成报告数据
            const reportData = generateReportData(filteredExpenses);

            // 根据选择的尺寸设置画布大小
            const sizeConfig = {
                'mobile': { width: 375, height: 1200, fontSize: '12px' },
                'tablet': { width: 768, height: 1400, fontSize: '14px' },
                'desktop': { width: 1200, height: 1000, fontSize: '16px' },
                'square': { width: 900, height: 900, fontSize: '14px' }
            };

            const config = sizeConfig[imageSize];

            // 创建临时容器用于渲染
            const tempContainer = document.createElement('div');
            tempContainer.style.position = 'fixed';
            tempContainer.style.top = '-9999px';
            tempContainer.style.left = '-9999px';
            tempContainer.style.width = config.width + 'px';
            tempContainer.style.height = config.height + 'px';
            tempContainer.style.backgroundColor = 'white';
            tempContainer.style.fontSize = config.fontSize;
            tempContainer.style.fontFamily = 'Microsoft YaHei, PingFang SC, Helvetica Neue, Arial, sans-serif';

            // 生成适合图片的HTML内容
            tempContainer.innerHTML = generateImageHTML(title, reportData, filteredExpenses, config);

            document.body.appendChild(tempContainer);

            // 显示加载提示
            const loadingModal = document.createElement('div');
            loadingModal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            loadingModal.innerHTML = `
                <div class="bg-white rounded-lg p-6 text-center">
                    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600 mx-auto mb-4"></div>
                    <p class="text-gray-700">正在生成图片报告...</p>
                </div>
            `;
            document.body.appendChild(loadingModal);

            // 使用html2canvas生成图片
            html2canvas(tempContainer, {
                width: config.width,
                height: config.height,
                scale: 2, // 提高清晰度
                useCORS: true,
                allowTaint: true,
                backgroundColor: '#ffffff'
            }).then(canvas => {
                // 清理临时元素
                document.body.removeChild(tempContainer);
                document.body.removeChild(loadingModal);

                // 下载图片
                const link = document.createElement('a');
                link.download = `${title.replace(/[^\w\s-]/g, '')}_${new Date().toISOString().split('T')[0]}.png`;
                link.href = canvas.toDataURL('image/png');
                link.click();

                closeImageReportModal();
            }).catch(error => {
                console.error('生成图片失败:', error);
                document.body.removeChild(tempContainer);
                document.body.removeChild(loadingModal);
                alert('生成图片失败，请重试');
            });
        }

        // 生成适合图片的HTML内容
        function generateImageHTML(title, data, expenses, config) {
            const categoryColors = {
                '餐饮': '#FF6B6B',
                '交通': '#4ECDC4',
                '购物': '#45B7D1',
                '住房': '#96CEB4',
                '娱乐': '#FFEAA7',
                '医疗': '#DDA0DD',
                '教育': '#98D8C8',
                '生活': '#F7DC6F',
                '其他': '#AED6F1'
            };

            // 排序分类数据
            const sortedCategories = Object.entries(data.categoryStats)
                .sort((a, b) => b[1].amount - a[1].amount)
                .slice(0, 8); // 限制显示前8个分类

            // 根据画布大小调整布局
            const isMobile = config.width <= 375;
            const isSquare = config.width === config.height;

            return `
                <div style="
                    width: 100%;
                    height: 100%;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    padding: ${isMobile ? '15px' : '20px'};
                    box-sizing: border-box;
                    display: flex;
                    flex-direction: column;
                ">
                    <!-- 头部 -->
                    <div style="
                        background: rgba(255,255,255,0.95);
                        border-radius: 15px;
                        padding: ${isMobile ? '15px' : '20px'};
                        text-align: center;
                        margin-bottom: ${isMobile ? '10px' : '15px'};
                        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
                    ">
                        <h1 style="
                            font-size: ${isMobile ? '18px' : '24px'};
                            font-weight: bold;
                            color: #333;
                            margin: 0 0 8px 0;
                        ">${title}</h1>
                        <p style="
                            font-size: ${isMobile ? '12px' : '14px'};
                            color: #666;
                            margin: 0;
                        ">${data.dateRange.start} 至 ${data.dateRange.end}</p>
                    </div>

                    <!-- 统计卡片 -->
                    <div style="
                        display: grid;
                        grid-template-columns: ${isMobile ? '1fr 1fr' : isSquare ? '1fr 1fr' : 'repeat(4, 1fr)'};
                        gap: ${isMobile ? '8px' : '12px'};
                        margin-bottom: ${isMobile ? '10px' : '15px'};
                    ">
                        <div style="
                            background: rgba(255,255,255,0.95);
                            border-radius: 10px;
                            padding: ${isMobile ? '12px' : '15px'};
                            text-align: center;
                            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                        ">
                            <div style="font-size: ${isMobile ? '10px' : '12px'}; color: #666; margin-bottom: 5px;">总支出</div>
                            <div style="font-size: ${isMobile ? '16px' : '20px'}; font-weight: bold; color: #e74c3c;">¥${data.totalAmount.toFixed(2)}</div>
                        </div>
                        <div style="
                            background: rgba(255,255,255,0.95);
                            border-radius: 10px;
                            padding: ${isMobile ? '12px' : '15px'};
                            text-align: center;
                            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                        ">
                            <div style="font-size: ${isMobile ? '10px' : '12px'}; color: #666; margin-bottom: 5px;">支出笔数</div>
                            <div style="font-size: ${isMobile ? '16px' : '20px'}; font-weight: bold; color: #333;">${data.totalCount}</div>
                        </div>
                        ${!isMobile || !isSquare ? `
                        <div style="
                            background: rgba(255,255,255,0.95);
                            border-radius: 10px;
                            padding: ${isMobile ? '12px' : '15px'};
                            text-align: center;
                            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                        ">
                            <div style="font-size: ${isMobile ? '10px' : '12px'}; color: #666; margin-bottom: 5px;">日均支出</div>
                            <div style="font-size: ${isMobile ? '16px' : '20px'}; font-weight: bold; color: #333;">¥${data.averageDaily.toFixed(2)}</div>
                        </div>
                        <div style="
                            background: rgba(255,255,255,0.95);
                            border-radius: 10px;
                            padding: ${isMobile ? '12px' : '15px'};
                            text-align: center;
                            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                        ">
                            <div style="font-size: ${isMobile ? '10px' : '12px'}; color: #666; margin-bottom: 5px;">支出天数</div>
                            <div style="font-size: ${isMobile ? '16px' : '20px'}; font-weight: bold; color: #333;">${Object.keys(data.dailyStats).length}</div>
                        </div>
                        ` : ''}
                    </div>

                    <!-- 分类统计 -->
                    <div style="
                        background: rgba(255,255,255,0.95);
                        border-radius: 15px;
                        padding: ${isMobile ? '15px' : '20px'};
                        flex: 1;
                        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
                        overflow: hidden;
                    ">
                        <h2 style="
                            font-size: ${isMobile ? '14px' : '18px'};
                            font-weight: bold;
                            color: #333;
                            margin: 0 0 ${isMobile ? '10px' : '15px'} 0;
                        ">📊 支出分类统计</h2>

                        <div style="
                            display: grid;
                            grid-template-columns: 1fr;
                            gap: ${isMobile ? '6px' : '8px'};
                            max-height: ${isMobile ? '300px' : '400px'};
                            overflow-y: auto;
                        ">
                            ${sortedCategories.map(([category, stats]) => `
                                <div style="
                                    display: flex;
                                    align-items: center;
                                    padding: ${isMobile ? '8px' : '10px'};
                                    background: #f8f9fa;
                                    border-radius: 8px;
                                    border-left: 4px solid ${categoryColors[category] || '#95a5a6'};
                                ">
                                    <div style="
                                        width: ${isMobile ? '12px' : '16px'};
                                        height: ${isMobile ? '12px' : '16px'};
                                        background-color: ${categoryColors[category] || '#95a5a6'};
                                        border-radius: 50%;
                                        margin-right: ${isMobile ? '8px' : '12px'};
                                    "></div>
                                    <div style="flex: 1;">
                                        <div style="
                                            font-weight: bold;
                                            font-size: ${isMobile ? '12px' : '14px'};
                                            color: #333;
                                            margin-bottom: 2px;
                                        ">${category}</div>
                                        <div style="
                                            font-size: ${isMobile ? '10px' : '12px'};
                                            color: #666;
                                        ">${stats.count} 笔 • ${stats.percentage}%</div>
                                        <div style="
                                            width: 100%;
                                            height: 4px;
                                            background: #e9ecef;
                                            border-radius: 2px;
                                            margin-top: 4px;
                                            overflow: hidden;
                                        ">
                                            <div style="
                                                width: ${stats.percentage}%;
                                                height: 100%;
                                                background-color: ${categoryColors[category] || '#95a5a6'};
                                                border-radius: 2px;
                                            "></div>
                                        </div>
                                    </div>
                                    <div style="
                                        font-size: ${isMobile ? '12px' : '16px'};
                                        font-weight: bold;
                                        color: #e74c3c;
                                        margin-left: ${isMobile ? '8px' : '12px'};
                                    ">¥${stats.amount.toFixed(2)}</div>
                                </div>
                            `).join('')}
                        </div>
                    </div>

                    <!-- 详细消费记录 -->
                    <div style="
                        background: rgba(255,255,255,0.95);
                        border-radius: 15px;
                        padding: ${isMobile ? '15px' : '20px'};
                        margin-top: ${isMobile ? '10px' : '15px'};
                        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
                    ">
                        <h2 style="
                            font-size: ${isMobile ? '14px' : '18px'};
                            font-weight: bold;
                            color: #333;
                            margin: 0 0 ${isMobile ? '10px' : '15px'} 0;
                        ">📝 详细消费记录</h2>

                        <div style="
                            max-height: ${isMobile ? '200px' : isSquare ? '250px' : '300px'};
                            overflow-y: auto;
                            border: 1px solid #e9ecef;
                            border-radius: 8px;
                        ">
                            <!-- 表头 -->
                            <div style="
                                display: grid;
                                grid-template-columns: ${isMobile ? '2fr 1fr 1.5fr' : '2fr 1.5fr 1fr 2fr'};
                                gap: ${isMobile ? '8px' : '12px'};
                                padding: ${isMobile ? '8px 12px' : '12px 16px'};
                                background: #f8f9fa;
                                border-bottom: 1px solid #e9ecef;
                                font-weight: bold;
                                font-size: ${isMobile ? '10px' : '12px'};
                                color: #333;
                            ">
                                <div>日期</div>
                                ${!isMobile ? '<div>类别</div>' : ''}
                                <div>金额</div>
                                <div>备注</div>
                            </div>

                            <!-- 记录列表 -->
                            ${expenses.sort((a, b) => new Date(b.date) - new Date(a.date)).slice(0, isMobile ? 15 : isSquare ? 20 : 25).map(expense => `
                                <div style="
                                    display: grid;
                                    grid-template-columns: ${isMobile ? '2fr 1fr 1.5fr' : '2fr 1.5fr 1fr 2fr'};
                                    gap: ${isMobile ? '8px' : '12px'};
                                    padding: ${isMobile ? '6px 12px' : '10px 16px'};
                                    border-bottom: 1px solid #f1f3f4;
                                    font-size: ${isMobile ? '9px' : '11px'};
                                    color: #333;
                                    align-items: center;
                                ">
                                    <div style="
                                        font-size: ${isMobile ? '9px' : '10px'};
                                    ">${expense.date.substring(5)}</div>
                                    ${!isMobile ? `
                                    <div style="
                                        display: flex;
                                        align-items: center;
                                        font-size: ${isMobile ? '9px' : '10px'};
                                    ">
                                        <span style="
                                            display: inline-block;
                                            width: 8px;
                                            height: 8px;
                                            background-color: ${categoryColors[expense.category] || '#95a5a6'};
                                            border-radius: 50%;
                                            margin-right: 6px;
                                        "></span>
                                        ${expense.category}
                                    </div>
                                    ` : ''}
                                    <div style="
                                        font-weight: bold;
                                        color: #e74c3c;
                                        font-size: ${isMobile ? '9px' : '11px'};
                                    ">¥${parseFloat(expense.amount).toFixed(2)}</div>
                                    <div style="
                                        font-size: ${isMobile ? '8px' : '10px'};
                                        color: #666;
                                        overflow: hidden;
                                        text-overflow: ellipsis;
                                        white-space: nowrap;
                                    ">${expense.note || '-'}</div>
                                </div>
                            `).join('')}

                            ${expenses.length > (isMobile ? 15 : isSquare ? 20 : 25) ? `
                                <div style="
                                    padding: ${isMobile ? '8px 12px' : '12px 16px'};
                                    text-align: center;
                                    font-size: ${isMobile ? '9px' : '11px'};
                                    color: #666;
                                    background: #f8f9fa;
                                ">
                                    还有 ${expenses.length - (isMobile ? 15 : isSquare ? 20 : 25)} 条记录未显示...
                                </div>
                            ` : ''}
                        </div>
                    </div>

                    <!-- 底部信息 -->
                    <div style="
                        text-align: center;
                        margin-top: ${isMobile ? '10px' : '15px'};
                        padding: ${isMobile ? '8px' : '10px'};
                        background: rgba(255,255,255,0.8);
                        border-radius: 8px;
                    ">
                        <p style="
                            font-size: ${isMobile ? '10px' : '12px'};
                            color: #666;
                            margin: 0;
                        ">生成时间：${new Date().toLocaleString('zh-CN')} | 生活NOTE支出管理系统</p>
                    </div>
                </div>
            `;
        }

        // 全局变量
        let expenseManager;

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，开始初始化...');

            // 初始化支出管理器
            expenseManager = new ExpenseManager();

            // 移动端菜单切换
            const mobileMenuButton = document.getElementById('mobile-menu-button');
            const mobileMenu = document.getElementById('mobile-menu');

            if (mobileMenuButton && mobileMenu) {
                mobileMenuButton.addEventListener('click', function() {
                    mobileMenu.classList.toggle('hidden');
                });
            }

            // 滚动时导航栏样式变化
            window.addEventListener('scroll', function() {
                const nav = document.querySelector('nav');
                if (nav) {
                    if (window.scrollY > 10) {
                        nav.classList.add('shadow-lg');
                    } else {
                        nav.classList.remove('shadow-lg');
                    }
                }
            });

            console.log('支出记录页面初始化完成');
        });
    </script>
</body>
</html>
