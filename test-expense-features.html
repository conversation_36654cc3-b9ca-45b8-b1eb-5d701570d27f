<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支出记录功能测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
</head>
<body class="bg-gray-50 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold text-center mb-8">支出记录功能测试</h1>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- 测试功能列表 -->
            <div class="bg-white rounded-lg p-6 shadow-md">
                <h2 class="text-xl font-bold mb-4 text-blue-600">
                    <i class="fa fa-check-circle mr-2"></i>新功能测试
                </h2>
                <div class="space-y-3">
                    <div class="flex items-center">
                        <i class="fa fa-arrow-right text-green-500 mr-2"></i>
                        <span>类别统一：食物 → 餐饮</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fa fa-arrow-right text-green-500 mr-2"></i>
                        <span>新增类别：生活</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fa fa-arrow-right text-green-500 mr-2"></i>
                        <span>支出记录编辑功能</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fa fa-arrow-right text-green-500 mr-2"></i>
                        <span>饼图显示金额和占比</span>
                    </div>
                </div>
            </div>

            <!-- 测试步骤 -->
            <div class="bg-white rounded-lg p-6 shadow-md">
                <h2 class="text-xl font-bold mb-4 text-purple-600">
                    <i class="fa fa-list-ol mr-2"></i>测试步骤
                </h2>
                <ol class="space-y-2 text-sm">
                    <li class="flex items-start">
                        <span class="bg-purple-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs mr-2 mt-0.5">1</span>
                        <span>打开支出记录页面</span>
                    </li>
                    <li class="flex items-start">
                        <span class="bg-purple-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs mr-2 mt-0.5">2</span>
                        <span>检查类别选项（应该有餐饮、生活，没有食物）</span>
                    </li>
                    <li class="flex items-start">
                        <span class="bg-purple-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs mr-2 mt-0.5">3</span>
                        <span>添加几条不同类别的支出记录</span>
                    </li>
                    <li class="flex items-start">
                        <span class="bg-purple-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs mr-2 mt-0.5">4</span>
                        <span>点击编辑按钮测试编辑功能</span>
                    </li>
                    <li class="flex items-start">
                        <span class="bg-purple-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs mr-2 mt-0.5">5</span>
                        <span>查看饼图，鼠标悬停查看占比</span>
                    </li>
                </ol>
            </div>
        </div>

        <!-- 快速跳转 -->
        <div class="mt-8 text-center">
            <a href="expense-tracker.html" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                <i class="fa fa-external-link mr-2"></i>打开支出记录页面
            </a>
        </div>

        <!-- 数据检查工具 -->
        <div class="mt-8 bg-white rounded-lg p-6 shadow-md">
            <h2 class="text-xl font-bold mb-4 text-orange-600">
                <i class="fa fa-database mr-2"></i>数据检查工具
            </h2>
            <div class="flex space-x-4 mb-4">
                <button onclick="checkCategories()" class="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded">
                    检查类别数据
                </button>
                <button onclick="simulateOldData()" class="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded">
                    模拟旧数据（食物类别）
                </button>
                <button onclick="clearTestData()" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded">
                    清除测试数据
                </button>
            </div>
            <div id="check-results" class="bg-gray-100 p-4 rounded text-sm"></div>
        </div>
    </div>

    <script>
        function checkCategories() {
            const expenses = JSON.parse(localStorage.getItem('expenses')) || [];
            const results = document.getElementById('check-results');
            
            let output = '<strong>类别数据检查结果：</strong><br><br>';
            
            if (expenses.length === 0) {
                output += '暂无支出数据<br>';
            } else {
                const categories = {};
                expenses.forEach(expense => {
                    if (!categories[expense.category]) {
                        categories[expense.category] = 0;
                    }
                    categories[expense.category]++;
                });
                
                output += `总共 ${expenses.length} 条记录<br>`;
                output += '类别分布：<br>';
                for (let category in categories) {
                    const color = category === '食物' ? 'color: red;' : 'color: green;';
                    output += `<span style="${color}">• ${category}: ${categories[category]} 条</span><br>`;
                }
                
                if (categories['食物']) {
                    output += '<br><span style="color: red; font-weight: bold;">⚠️ 发现食物类别，需要修复</span>';
                } else {
                    output += '<br><span style="color: green; font-weight: bold;">✅ 没有发现食物类别</span>';
                }
            }
            
            results.innerHTML = output;
        }

        function simulateOldData() {
            const expenses = JSON.parse(localStorage.getItem('expenses')) || [];
            
            // 添加一些包含"食物"类别的测试数据
            const testExpenses = [
                {
                    id: 'test_food_1',
                    date: '2024-01-15',
                    category: '食物',
                    amount: 25.50,
                    note: '午餐'
                },
                {
                    id: 'test_food_2',
                    date: '2024-01-16',
                    category: 'food',
                    amount: 18.00,
                    note: '早餐'
                }
            ];
            
            // 移除已存在的测试数据
            const filteredExpenses = expenses.filter(e => !e.id.startsWith('test_food_'));
            
            // 添加新的测试数据
            const newExpenses = [...filteredExpenses, ...testExpenses];
            localStorage.setItem('expenses', JSON.stringify(newExpenses));
            
            document.getElementById('check-results').innerHTML = 
                '<span style="color: blue;">已添加包含"食物"类别的测试数据，请刷新支出记录页面查看修复效果</span>';
        }

        function clearTestData() {
            const expenses = JSON.parse(localStorage.getItem('expenses')) || [];
            const filteredExpenses = expenses.filter(e => !e.id.startsWith('test_food_'));
            localStorage.setItem('expenses', JSON.stringify(filteredExpenses));
            
            document.getElementById('check-results').innerHTML = 
                '<span style="color: green;">测试数据已清除</span>';
        }

        // 页面加载时自动检查
        window.addEventListener('load', checkCategories);
    </script>
</body>
</html>
