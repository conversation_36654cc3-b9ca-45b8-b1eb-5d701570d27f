<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>本地存储分析器</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
</head>
<body class="bg-gray-50 p-8">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-3xl font-bold text-center mb-8">本地存储分析器</h1>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <!-- 存储概览 -->
            <div class="bg-white rounded-lg p-6 shadow-md">
                <h2 class="text-xl font-bold mb-4 text-blue-600">
                    <i class="fa fa-database mr-2"></i>存储概览
                </h2>
                <div id="storage-overview" class="space-y-3">
                    <!-- 动态生成 -->
                </div>
            </div>

            <!-- 存储详情 -->
            <div class="bg-white rounded-lg p-6 shadow-md">
                <h2 class="text-xl font-bold mb-4 text-green-600">
                    <i class="fa fa-pie-chart mr-2"></i>存储详情
                </h2>
                <div id="storage-details" class="space-y-3">
                    <!-- 动态生成 -->
                </div>
            </div>
        </div>

        <!-- 数据分析 -->
        <div class="bg-white rounded-lg p-6 shadow-md mb-8">
            <h2 class="text-xl font-bold mb-4 text-purple-600">
                <i class="fa fa-bar-chart mr-2"></i>数据分析
            </h2>
            <div id="data-analysis" class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <!-- 动态生成 -->
            </div>
        </div>

        <!-- 存储管理 -->
        <div class="bg-white rounded-lg p-6 shadow-md">
            <h2 class="text-xl font-bold mb-4 text-orange-600">
                <i class="fa fa-cogs mr-2"></i>存储管理
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                <button onclick="refreshAnalysis()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                    <i class="fa fa-refresh mr-2"></i>刷新分析
                </button>
                <button onclick="exportAllData()" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded">
                    <i class="fa fa-download mr-2"></i>导出所有数据
                </button>
                <button onclick="clearOldData()" class="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded">
                    <i class="fa fa-trash mr-2"></i>清理旧数据
                </button>
                <button onclick="showStorageInfo()" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded">
                    <i class="fa fa-info mr-2"></i>存储说明
                </button>
            </div>
            <div id="management-results" class="bg-gray-100 p-4 rounded text-sm"></div>
        </div>

        <!-- 返回链接 -->
        <div class="mt-8 text-center">
            <a href="main-plan.html" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-medium transition-colors mr-4">
                <i class="fa fa-home mr-2"></i>主计划页面
            </a>
            <a href="expense-tracker.html" class="bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-lg font-medium transition-colors mr-4">
                <i class="fa fa-money mr-2"></i>支出记录页面
            </a>
            <a href="final-test.html" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                <i class="fa fa-list mr-2"></i>测试首页
            </a>
        </div>
    </div>

    <script>
        // 计算字符串的字节大小（UTF-16）
        function getByteSize(str) {
            return new Blob([str]).size;
        }

        // 格式化字节大小
        function formatBytes(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 分析localStorage使用情况
        function analyzeStorage() {
            const analysis = {
                total: 0,
                items: [],
                breakdown: {}
            };

            // 遍历所有localStorage项
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                const value = localStorage.getItem(key);
                const size = getByteSize(value);
                
                analysis.items.push({
                    key: key,
                    size: size,
                    value: value
                });
                
                analysis.total += size;
            }

            // 按大小排序
            analysis.items.sort((a, b) => b.size - a.size);

            return analysis;
        }

        // 分析具体数据内容
        function analyzeDataContent() {
            const content = {
                dailyPlanner: null,
                expenses: null
            };

            try {
                const plannerData = localStorage.getItem('dailyPlanner');
                if (plannerData) {
                    content.dailyPlanner = JSON.parse(plannerData);
                }
            } catch (e) {
                console.error('解析dailyPlanner数据失败:', e);
            }

            try {
                const expenseData = localStorage.getItem('expenses');
                if (expenseData) {
                    content.expenses = JSON.parse(expenseData);
                }
            } catch (e) {
                console.error('解析expenses数据失败:', e);
            }

            return content;
        }

        // 显示存储概览
        function displayStorageOverview(analysis) {
            const container = document.getElementById('storage-overview');
            const totalMB = analysis.total / (1024 * 1024);
            const usagePercent = (totalMB / 5) * 100; // 假设5MB限制

            container.innerHTML = `
                <div class="flex justify-between items-center p-3 bg-blue-50 rounded">
                    <span class="font-medium">总使用量</span>
                    <span class="text-blue-600 font-bold">${formatBytes(analysis.total)}</span>
                </div>
                <div class="flex justify-between items-center p-3 bg-gray-50 rounded">
                    <span class="font-medium">存储项数量</span>
                    <span class="text-gray-600">${analysis.items.length} 项</span>
                </div>
                <div class="p-3 bg-yellow-50 rounded">
                    <div class="flex justify-between items-center mb-2">
                        <span class="font-medium">使用率</span>
                        <span class="text-yellow-600">${usagePercent.toFixed(2)}%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-yellow-500 h-2 rounded-full" style="width: ${Math.min(usagePercent, 100)}%"></div>
                    </div>
                    <div class="text-xs text-gray-500 mt-1">基于5MB限制估算</div>
                </div>
            `;
        }

        // 显示存储详情
        function displayStorageDetails(analysis) {
            const container = document.getElementById('storage-details');
            
            let html = '';
            analysis.items.forEach(item => {
                const percent = (item.size / analysis.total * 100).toFixed(1);
                html += `
                    <div class="flex justify-between items-center p-2 border-b">
                        <div>
                            <span class="font-medium">${item.key}</span>
                            <div class="text-xs text-gray-500">${percent}%</div>
                        </div>
                        <span class="text-sm text-gray-600">${formatBytes(item.size)}</span>
                    </div>
                `;
            });

            container.innerHTML = html || '<p class="text-gray-500">暂无数据</p>';
        }

        // 显示数据分析
        function displayDataAnalysis(content) {
            const container = document.getElementById('data-analysis');
            
            let plannerStats = { tasks: 0, coreTasks: 0, improvements: 0, history: 0 };
            let expenseStats = { total: 0, categories: 0 };

            // 分析计划数据
            if (content.dailyPlanner) {
                const data = content.dailyPlanner;
                plannerStats.tasks = Object.keys(data.tasks || {}).reduce((sum, date) => sum + (data.tasks[date]?.length || 0), 0);
                plannerStats.coreTasks = Object.keys(data.coreTasks || {}).reduce((sum, date) => sum + (data.coreTasks[date]?.length || 0), 0);
                plannerStats.improvements = (data.improvements || []).length;
                plannerStats.history = Object.keys(data.history || {}).length;
            }

            // 分析支出数据
            if (content.expenses) {
                expenseStats.total = content.expenses.length;
                const categories = new Set(content.expenses.map(e => e.category));
                expenseStats.categories = categories.size;
            }

            container.innerHTML = `
                <div class="bg-blue-50 p-4 rounded">
                    <h3 class="font-bold text-blue-800 mb-2">计划数据</h3>
                    <div class="space-y-1 text-sm">
                        <div>今日任务: ${plannerStats.tasks}</div>
                        <div>核心任务: ${plannerStats.coreTasks}</div>
                        <div>改进项: ${plannerStats.improvements}</div>
                        <div>历史记录: ${plannerStats.history} 天</div>
                    </div>
                </div>
                <div class="bg-green-50 p-4 rounded">
                    <h3 class="font-bold text-green-800 mb-2">支出数据</h3>
                    <div class="space-y-1 text-sm">
                        <div>支出记录: ${expenseStats.total}</div>
                        <div>消费类别: ${expenseStats.categories}</div>
                    </div>
                </div>
                <div class="bg-purple-50 p-4 rounded">
                    <h3 class="font-bold text-purple-800 mb-2">预估增长</h3>
                    <div class="space-y-1 text-sm">
                        <div>每月任务: ~${plannerStats.tasks * 2}</div>
                        <div>每月支出: ~${expenseStats.total * 2}</div>
                        <div class="text-xs text-gray-500 mt-2">基于当前使用频率估算</div>
                    </div>
                </div>
            `;
        }

        // 刷新分析
        function refreshAnalysis() {
            const analysis = analyzeStorage();
            const content = analyzeDataContent();
            
            displayStorageOverview(analysis);
            displayStorageDetails(analysis);
            displayDataAnalysis(content);
            
            document.getElementById('management-results').innerHTML = 
                '<span style="color: green;">✅ 分析已刷新</span>';
        }

        // 导出所有数据
        function exportAllData() {
            const allData = {};
            
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                try {
                    allData[key] = JSON.parse(localStorage.getItem(key));
                } catch (e) {
                    allData[key] = localStorage.getItem(key);
                }
            }
            
            const dataStr = JSON.stringify(allData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `本地存储备份_${new Date().toISOString().split('T')[0]}.json`;
            link.click();
            
            document.getElementById('management-results').innerHTML = 
                '<span style="color: green;">✅ 数据已导出</span>';
        }

        // 清理旧数据
        function clearOldData() {
            if (!confirm('确定要清理30天前的历史数据吗？此操作不可撤销。')) return;
            
            try {
                const plannerData = JSON.parse(localStorage.getItem('dailyPlanner') || '{}');
                const thirtyDaysAgo = new Date();
                thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
                
                let cleaned = 0;
                if (plannerData.history) {
                    Object.keys(plannerData.history).forEach(dateKey => {
                        const date = new Date(dateKey);
                        if (date < thirtyDaysAgo) {
                            delete plannerData.history[dateKey];
                            cleaned++;
                        }
                    });
                }
                
                localStorage.setItem('dailyPlanner', JSON.stringify(plannerData));
                
                document.getElementById('management-results').innerHTML = 
                    `<span style="color: orange;">🗑️ 已清理 ${cleaned} 天的历史数据</span>`;
                    
                refreshAnalysis();
            } catch (e) {
                document.getElementById('management-results').innerHTML = 
                    '<span style="color: red;">❌ 清理失败: ' + e.message + '</span>';
            }
        }

        // 显示存储说明
        function showStorageInfo() {
            document.getElementById('management-results').innerHTML = `
                <div class="text-sm space-y-2">
                    <h4 class="font-bold">localStorage 存储说明：</h4>
                    <p>• <strong>存储位置</strong>：浏览器本地，不会上传到服务器</p>
                    <p>• <strong>容量限制</strong>：大多数浏览器为 5-10MB</p>
                    <p>• <strong>数据格式</strong>：JSON 字符串，UTF-16 编码</p>
                    <p>• <strong>持久性</strong>：除非手动清除或浏览器清理，否则永久保存</p>
                    <p>• <strong>隐私性</strong>：仅当前域名可访问，其他网站无法读取</p>
                    <p>• <strong>同步性</strong>：不会在不同设备间同步</p>
                </div>
            `;
        }

        // 页面加载时自动分析
        window.addEventListener('load', refreshAnalysis);
    </script>
</body>
</html>
